#!/bin/bash

mkdir -p /root/.ssh/
touch /root/.ssh/config
grep -q 'StrictHostKeyChecking' /root/.ssh/config || echo 'StrictHostKeyChecking no' >> /root/.ssh/config

yes | ssh-keygen -b 2048 -t rsa -f /root/.ssh/id_rsa -q -N ''

cp /root/.ssh/id_rsa /home/<USER>/.ssh/
cp /root/.ssh/id_rsa.pub /home/<USER>/.ssh/

chown vagrant /home/<USER>/.ssh/id_rsa
chown vagrant /home/<USER>/.ssh/id_rsa.pub

cp /root/.ssh/id_rsa.pub /usr/src/cluster/

apt-get update

apt-get install -y sshpass python3.8
