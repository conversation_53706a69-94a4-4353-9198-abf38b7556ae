# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/focal64"
  # v20221107.0.0
  config.vm.box_version = "20210720.0.1"
  config.vm.hostname = "local"
  config.vm.network :private_network, ip: "*************"
  config.vm.network :forwarded_port, guest: 30500, host: 30500, host_ip: "127.0.0.1" # registry
  # config.vm.network :forwarded_port, guest: 30501, host: 30501, host_ip: "127.0.0.1" # jenkins
  # config.vm.network :forwarded_port, guest: 30502, host: 30502, host_ip: "127.0.0.1" # openvpn
  # config.vm.network :forwarded_port, guest: 30503, host: 30503, host_ip: "127.0.0.1" # openvpn2
  # config.vm.network :forwarded_port, guest: 30504, host: 30504, host_ip: "127.0.0.1" # ingress http, 30505 reserved
  # config.vm.network :forwarded_port, guest: 30900, host: 30900, host_ip: "127.0.0.1" # prometheus
  # config.vm.network :forwarded_port, guest: 30902, host: 30902, host_ip: "127.0.0.1" # grafana
  # config.vm.network :forwarded_port, guest: 30903, host: 30903, host_ip: "127.0.0.1" # alertmanager
  config.vm.network :forwarded_port, guest: 30904, host: 30904, host_ip: "127.0.0.1" # kibana
  config.vm.network :forwarded_port, guest: 30905, host: 30905, host_ip: "127.0.0.1" # elasticsearch
  # config.vm.network :forwarded_port, guest: 31000, host: 31000, host_ip: "127.0.0.1" # ks-web
  # config.vm.network :forwarded_port, guest: 31001, host: 31001, host_ip: "127.0.0.1" # zalenium
  # config.vm.network :forwarded_port, guest: 31002, host: 31002, host_ip: "127.0.0.1" # ks-ui-tests
  config.vm.network :forwarded_port, guest: 31003, host: 31003, host_ip: "127.0.0.1" # postgres
  config.vm.network :forwarded_port, guest: 31004, host: 31004, host_ip: "127.0.0.1" # ks-static-file-server
  # config.vm.network :forwarded_port, guest: 31005, host: 31005, host_ip: "127.0.0.1" # redash
  # config.vm.network :forwarded_port, guest: 31006, host: 31006, host_ip: "127.0.0.1" # redis
  config.vm.network :forwarded_port, guest: 31007, host: 31007, host_ip: "127.0.0.1" # ks-web-prod
  # config.vm.network :forwarded_port, guest: 31008, host: 31008, host_ip: "127.0.0.1" # ks-web-staging
  # config.vm.network :forwarded_port, guest: 31009, host: 31009, host_ip: "127.0.0.1" # sonarqube
  # config.vm.network :forwarded_port, guest: 31010, host: 31010, host_ip: "127.0.0.1" # zimbra-web
  # config.vm.network :forwarded_port, guest: 31011, host: 31011, host_ip: "127.0.0.1" # zimbra something

  config.vm.synced_folder ".", "/usr/src/cluster",
    id: "cluster",
    owner: "vagrant",
    group: "vagrant",
    mount_options: ["dmode=700,fmode=700"]

  if File.directory?(File.expand_path("../secrets"))
    config.vm.synced_folder "../secrets", "/usr/src/secrets",
      id: "secrets",
      owner: "vagrant",
      group: "vagrant",
      mount_options: ["dmode=700,fmode=700"]
  end

  config.vm.provider "virtualbox" do |vb|
    vb.gui = false
    vb.memory = "2120"
    vb.cpus = 3
  end

  config.vm.provision :shell do |shell|
    shell.path = "./hack/provision-vagrant.sh"
  end

  config.vm.provision "ansible_local" do |ansible|
    ansible.playbook = "/usr/src/cluster/ansible/empty.yml"
  end
end
