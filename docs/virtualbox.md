# Virtualbox

## How to take a snapshot of vm

```bash
vagrant snapshot save default your-snapshot-name-here
```

## Start Km

```bash
vboxmanage startvm km-staging --type=headless
```

## Stop Km

```bash
vboxmanage controlvm km-staging poweroff --soft
```

## See running vms

```bash
vboxmanage list runningvms

vboxmanage list vms --long | grep -e "Name:" -e "State:"
```

## Snapshots

```bash
VBoxManage snapshot km-staging take configure-vm --live

VBoxManage snapshot km-staging restore configure-vm

VBoxManage snapshot km-staging list
```

## Logs

```bash
cd ~/VirtualBox\ VMs/km/Logs
```

## Debugging fantom crashes

```bash
VBoxManage debugvm km info ahci0
```

[Virtual HDD becomes unavailable for guest : with AHCI#0: Port x reset (virtualbox.org)](https://www.virtualbox.org/ticket/15374)
