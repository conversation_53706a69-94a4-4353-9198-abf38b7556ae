# Disk usage

du -hd 1

## Order by size

sudo du -hd 1 / | sort -nr

# Journalctl

[How To Use Journalctl to View and Manipulate Systemd Logs](https://www.digitalocean.com/community/tutorials/how-to-use-journalctl-to-view-and-manipulate-systemd-logs)

## <PERSON>ys logs

journalctl

journalctl -u kubelet

journalctl --since "5 min ago"

journalctl -t openvpn

journalctl --utc --since "2022-11-15 19:00" --until "2022-11-15 22:10" -u kubelet

# Install security updates

[A Few Tips On How To Manage Updates (askubuntu)](https://askubuntu.com/a/217999)

apt-get -s dist-upgrade | grep "^Inst" | grep -i securi | awk -F " " {'print $2'} | xargs apt-get install -y

# SSH

scp km@************:/mnt/hdd/cluster-data/ks-web-prod-pv/ee0d0868-95a9-4331-bd31-976e44e9cd80 .

# Networking

## See connections (can add --listening)

netstat --tcp --programs --numeric --listening

nmap **************
nmap -p 80,443,31003 **************

## Kube networking playlist

[Kubernetes Networking Series (youtube)](https://www.youtube.com/playlist?list=PLSAko72nKb8QWsfPpBlsw-kOdMBD7sra-)

## Debug packets

tshark -V -f "tcp port 445" -i any

## Windows flush dns

ipconfig //flushdns

## Check if port open

telnet ************* 445

## How to use tar command

To create a .tar.gz file from a single file:
tar -czvf filename.tar.gz /path/to/file

To create a .tar.gz file from a directory:
tar -czvf filename.tar.gz /path/to/dir1

To create a .tar.gz file from multiple directories and files:
tar -czvf filename.tar.gz /path/to/dir1 dir2 file1 file2

To create a .tar.gz file containing all PDF files (*.pdf) in the current directory:
tar -czvf archive.tgz *.pdf
