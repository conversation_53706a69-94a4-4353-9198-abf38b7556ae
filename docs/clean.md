# Clean data on cluster

## Remove old videos

```bash
sudo find /mnt/hdd/cluster-data/ks-web-prod-pv/ -mindepth 2 -maxdepth 4 -mtime +4 -exec rm -rf {} \;
```

## Remove images from docker-registry:

```bash
kubectl scale deployment docker-registry -n=registry --replicas=0

sudo rm -rf /mnt/hdd/cluster-data/docker-registry-pv/*

kubectl scale deployment docker-registry -n=registry --replicas=1
```

After this deployment script has to run to put ks image into the registry.

## Remove all unused containers, networks, images:

```bash
sudo crictl --runtime-endpoint unix:///var/run/containerd/containerd.sock rmi --prune
```

## Remove wal-archive logs (it is disabled for now, so no need to run it)

```bash
sudo find /mnt/hdd/cluster-data/postgres-pv/wal-archive -mindepth 1 -maxdepth 1 -type f -mtime +1 -exec rm -rf {} \;
```

## If evicted because of not enough space

```bash
kgpo -n kubernetes-dashboard

krmpo -n kubernetes-dashboard kubernetes-dashboard-79b875f7f8-hmphz

kubectl delete pod --all-namespaces --field-selector 'status.phase==Failed'
```
