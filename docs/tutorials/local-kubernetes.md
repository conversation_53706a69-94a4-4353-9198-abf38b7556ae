# Start work with cluster

Clone the repository and go in the root folder:

```bash
git clone https://gitlab.com/flexdev/cluster.git
```

```bash
cd cluster
```

Add two plugins:

```bash
vagrant plugin install vagrant-disksize vagrant-vbguest
```

Create and configure guest machine:

```bash
vagrant up
```

## Set up environment

Go to folder ansible. Open a file local.yml.
Uncomment strings from start to (inclusive):

```text
-...
-...
-kube/config-master
```

Your guest machine must be in state running at this time. To check this, enter

```bash
vagrant global-status --prune
```

You should see something like this:

```text
id       name    provider   state   directory
--------------------------------------------------
f82d065  default virtualbox running C:/dev/cluster
```

Open command prompt in  directory cluster. Enter:

```bash
vagrant ssh -c "(cd /usr/src/cluster/ansible && sudo ansible-playbook local.yml)"
```

After finished, enter:

```bash
./hack/post-init.sh
```

If all done - you configured environments.

## Get access to Kubernetes dashboard

Before this point, your guest machine must be in running state and environment for your virtual machine should be configured.

Run:

```bash
kubectl proxy
```

Generate token (it will expire in 2232 hours, so you will have to repeat these steps again):

```bash
kubectl -n kubernetes-dashboard create token --duration 2232h dashboard-admin
```

Insert token into your kube config (in C/Users/<USER>/.kube/config) for user. Add new key: token. In value of this key insert you token. Example:

```text
users:
- name: kubernetes-admin
  user:
    client-certificate-data: LSkFURSBLI6IkxITjZIU3U4UdJSBVS0...
    client-key-data: LS0tLS1CRUdJSBLRVktLtLQoI6IkxITjZIU3U4V...
    token: eyJhbGciOiJSUzI1NiIsImtpZCI6IkxITjZIU3U4VUdJSXlWe...
```

Open [kubernetes-dashboard](http://localhost:8001/api/v1/namespaces/kubernetes-dashboard/services/https:kubernetes-dashboard:/proxy).
Chose Kubeconfig checkbox. Choose config file with token (C/Users/<USER>/.kube/config). Press Sign in.

## If you have error "Permission denied (publickey)"

If during

```bash
vagrant ssh -c "some_command"
```

 you'll get an error

```text
..Permission denied (publickey)
```

, then run:

```bash
./hack/copy-vagrant-key-km.sh
```
## Ips and ssh

km-vps:
ssh km@*************

km-staging:
ssh km@*************
