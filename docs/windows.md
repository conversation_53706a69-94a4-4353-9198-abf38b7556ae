# Packages for local windows development

Use chocolatey

choco install kubernetes-cli --version=1.29.1

choco install kubernetes-helm --version=3.14.0

## Talos on windows

Use talosctl from wsl, add to your bash aliases `alias t="wsl talosctl"`. Restart command line and now you can use `t` command.


# WSL initial setup

Install WSL 2

Then in wsl command line run:

sudo apt update && sudo apt upgrade

sudo apt install ansible=2.9.6+dfsg-1

Install talos:

curl -sL https://talos.dev/install | sh

To browse files paste into windows explorer:

\\wsl.localhost\Ubuntu\home\ubuntu
