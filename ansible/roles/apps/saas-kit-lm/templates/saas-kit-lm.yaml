apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{lm_name}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{lm_name}}
  template:
    metadata:
      labels:
        app: {{lm_name}}
    spec:
      initContainers:
        - name: lm-init
          image: {{local_registry_address}}/lm-init:latest
          env:
            - name: POSTGRESQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: postgres-password
            - name: FD_DATABASE_CONNECTION_STRING
              value: *************************************************************************
      containers:
        - name: {{lm_name}}
          image: {{local_registry_address}}/{{lm_name}}:latest
          env:
            - name: POSTGRESQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: postgres-password
            - name: FD_DATABASE_CONNECTION_STRING
              value: *************************************************************************
          ports:
            - containerPort: 3000

---
apiVersion: v1
kind: Service
metadata:
  name: {{lm_name}}-service
spec:
  selector:
    app: {{lm_name}}
  ports:
    - protocol: TCP
      port: 3000
  type: NodePort
