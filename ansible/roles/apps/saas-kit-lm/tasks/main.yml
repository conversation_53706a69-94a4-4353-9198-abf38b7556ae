- name: Create namespace
  shell: kube<PERSON>l create namespace {{lm_name}} --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Deploy
  shell: kubectl apply -n {{lm_name}} -f -
  args:
    stdin: "{{ lookup('template', '{{lm_templates_dir}}/saas-kit-lm.yaml')}}"

- name: Create ingress
  shell: kubectl apply -n {{lm_name}} -f -
  args:
    stdin: "{{ lookup('template', '{{lm_templates_dir}}/lm-ingress.yaml')}}"
