apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ks-static-file-server-ingress
  namespace: static-file-server
spec:
  ingressClassName: nginx
  rules:
    - host: static.{{domain}}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ks-static-file-server
                port:
                  number: 80
