- name: Create namespace
  shell: kubectl create namespace static-file-server --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Create pv and pvc
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{lookup('template', '{{apps_dir}}/ks-static-file-server/templates/ks-static-file-server-pv-pvc.yml')}}"

- name: Setup ingress
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/ks-static-file-server/templates/ks-static-file-server-ingress.yml')}}"
