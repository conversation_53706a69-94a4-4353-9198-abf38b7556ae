apiVersion: v1
kind: PersistentVolume
metadata:
  name: jupyter-user-pv
spec:
  capacity:
    storage: 30Gi
  accessModes:
    - ReadWriteOnce
  storageClassName: local-storage
  local:
      path: "/usr/local/etc/jupyterhub"
  persistentVolumeReclaimPolicy: Retain
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
            - key: node-role.kubernetes.io/control-plane
              operator: Exists
