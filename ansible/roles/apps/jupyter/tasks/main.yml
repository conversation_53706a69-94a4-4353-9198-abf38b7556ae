- name: Create namespace
  shell: kubectl create namespace jupyter --dry-run=client -o yaml | kubectl apply -f -

- name: Add jupyterhub
  shell: helm repo add jupyterhub https://jupyterhub.github.io/helm-chart/

- name: Update repo
  shell: helm repo update

- name: Create pv
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{apps_dir}}/jupyter/templates/jupyter-pv.yaml')}}"

- name: Create users pv and pvc
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{apps_dir}}/jupyter/templates/users-pv-pvc.yaml')}}"

- name: Setup ingress
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{apps_dir}}/jupyter/templates/jupyter-ingress.yaml')}}"

