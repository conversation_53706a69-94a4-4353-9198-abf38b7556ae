- name: Create namespace
  shell: kube<PERSON>l create namespace {{app_name}}-staging --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Deploy
  shell: kubectl apply -n saas-kit-staging -f -
  args:
    stdin: "{{ lookup('template', '{{saas_kit_templates_dir}}/saas-kit-staging.yaml')}}"

- name: Create ingress
  shell: kubectl apply -n saas-kit-staging -f -
  args:
    stdin: "{{ lookup('template', '{{saas_kit_templates_dir}}/saas-kit-ingress.yaml')}}"
