apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{app_name}}-ingress
  namespace: {{app_name}}-staging
spec:
  ingressClassName: nginx
  rules:
    - host: {{app_name}}-staging.{{domain}}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{app_name}}-service
                port:
                  number: 3000
