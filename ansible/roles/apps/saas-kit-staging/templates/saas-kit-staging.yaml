apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{app_name}}-staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{app_name}}-staging
  template:
    metadata:
      labels:
        app: {{app_name}}-staging
    spec:
      initContainers:
        - name: {{app_name}}-init
          image: {{local_registry_address}}/{{app_name}}-init:latest
          env:
            - name: POSTGRESQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: postgres-password
            - name: FD_DATABASE_CONNECTION_STRING
              value: *****************************************************************************
            - name: FD_DATABASE_NAME
              value: {{app_db_name}}
      containers:
        - name: {{app_name}}
          image: {{local_registry_address}}/{{app_name}}:latest
          env:
            - name: POSTGRESQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: postgres-password
            - name: FD_DATABASE_CONNECTION_STRING
              value: *****************************************************************************
            - name: FD_DATABASE_NAME
              value: {{app_db_name}}
          ports:
            - containerPort: 3000

---
apiVersion: v1
kind: Service
metadata:
  name: {{app_name}}-service
spec:
  selector:
    app: {{app_name}}-staging
  ports:
    - protocol: TCP
      port: 3000
  type: NodePort
