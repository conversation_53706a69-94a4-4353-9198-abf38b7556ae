- name: Create namespace
  shell: kube<PERSON>l create namespace sonarqube --dry-run=client -o yaml | kubectl apply -f -

- name: Get postgresql-password from postgresql-secret
  shell: "kubectl get secrets/postgresql-secret -n sonarqube -o jsonpath='{.data.postgresql-password}' | base64 --decode"
  register: psql_password

- set_fact:
    error_text: "ERROR:  database \"sonarDB\" already exists"

- name: Create sonarDB
  shell: "kubectl exec --namespace=postgres -it postgres-postgresql-0 -- psql 'user=postgres password={{psql_password.stdout}} host=localhost port=5432' -c 'CREATE DATABASE \"sonarDB\";'"
  register: psql_res
  failed_when:
     - "error_text not in psql_res.stdout_lines"
  retries: 10
  delay: 10
  until:
     - "(error_text in psql_res.stdout_lines) or (error_text in psql_res.stderr_lines)"
  ignore_errors: true

- name: Update helm repo
  shell: helm repo update

- name: Add repo
  shell: helm repo add sonarqube https://SonarSource.github.io/helm-chart-sonarqube

- name: Create sonarqube-sonarqube secret which will later contain password
  shell: kubectl create secret generic sonarqube-sonarqube --namespace=sonarqube --from-literal=secret=secret

- name: Install sonarqube
  shell: helm install sonarqube --namespace sonarqube -f - sonarqube/sonarqube --version 7.0.0+449
  args:
    stdin: "{{ lookup('template', '{{sonarqube_templates_dir}}/sonarqube-overrides.yml')}}"

- name: Set sonarqube node port
  shell: "kubectl patch svc sonarqube-sonarqube --namespace sonarqube -p '{\"spec\": {\"ports\": [{\"name\": \"http\",\"protocol\": \"TCP\",\"port\": 9000,\"targetPort\": \"http\",\"nodePort\": 31009}]}}'"
