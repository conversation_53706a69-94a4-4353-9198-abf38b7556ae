postgresql:
  enabled: false
service:
  type: NodePort
resources:
  limits:
    cpu: '0'
    memory: '0'
  requests:
    cpu: '0'
    memory: '0'
prometheusExporter:
  enabled: false
account:
  resources:
    limits:
      cpu: '0'
      memory: '0'
    requests:
      cpu: '0'
      memory: '0'
jdbcOverwrite:
  enable: true
  jdbcUsername: postgres
  jdbcUrl: jdbc:postgresql://{{ansible_host}}:31003/sonarDB?socketTimeout=1500


# https://github.com/Oteemo/charts/blob/master/charts/sonarqube/values.yaml
