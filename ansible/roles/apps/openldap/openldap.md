# Manual parts

[OpenLDAP Helm Chart (artifacthub)](https://artifacthub.io/packages/helm/helm-openldap/openldap-stack-ha)

## Add ca.crt to openldap-tls secret

From openldap-tls tls.crt take 2nd section with BEGIN SERTIFICATE, base64 encode it and add to openldap-tls with ca.crt key.

### Delete openldap

```bash
helm uninstall -n openldap openldap

sudo rm -rf /mnt/hdd/cluster-data/openldap-pv
```

delete ingres and pv pvc

### Non ingress url

```bash
kup
```

http://localhost:8001/api/v1/namespaces/openldap/services/openldap-phpldapadmin:80/proxy/

### How To Use LDIF Files to Make Changes to an OpenLDAP System

[How To Use LDIF Files to Make Changes to an OpenLDAP System](https://www.digitalocean.com/community/tutorials/how-to-use-ldif-files-to-make-changes-to-an-openldap-system)

### Ports

636 tls

389

### ldapsearch

```bash
kubectl exec -n openldap openldap-0 -- sh -c "ldapsearch -b dc=fernir,dc=co -x -D 'cn=Tolik Levytskyi,ou=People,dc=fernir,dc=co' -w password -H ldaps://"

kubectl exec -n openldap openldap-0 -- sh -c "ldapsearch -Y EXTERNAL -H ldapi:// -b dc=fernir,dc=co"

kubectl exec -n openldap openldap-0 -- sh -c "ldapsearch -Y EXTERNAL -H ldapi:// -b cn=config"
```

### ldapmodify

```bash
kubectl exec -n openldap openldap-0 -- sh -c "ldapmodify -x -D 'cn=admin,dc=fernir,dc=co' -w password -H ldap:// <<!
dn: ou=People,dc=fernir,dc=co
changetype: add
objectClass: organizationalUnit
ou: People
!
"
```

### Generate password

```bash
slappasswd -s password -h {CRYPT} -c \$6\$rounds=500000\$%.16s -u -n
```

### Hardening

[OpenLDAP Server Installation, Configuration and Hardening](https://archive.manintheit.org/posts/gnulinux/openldap-server-installation-configuration-hardening/)

[Securing openldap-servers with SSL/TLS on RHEL7](https://access.redhat.com/articles/1474813)

pwd policy
[Use ppolicy_hash_cleartext to keep OpenLDAP from storing and returning plain text passwords](https://www.strehle.de/tim/weblog/archives/2015/08/27/1567)

pwd [LDAP and password encryption strength](https://www.redpill-linpro.com/techblog/2016/08/16/ldap-password-hash.html)

pwd [How can I change the password policy of LDAP?](https://kb.brightcomputing.com/knowledge-base/how-can-i-change-the-password-policy-of-ldap/)

### ACL - Access Control

[Access Control](https://www.openldap.org/doc/admin24/access-control.html)
