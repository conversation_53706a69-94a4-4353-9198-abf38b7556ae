- name: Create pv directory
  file:
    path: "{{cluster_data_dir}}/openldap-pv"
    state: directory

- name: Create namespace
  shell: kubectl create namespace openldap --dry-run=client -o yaml | kubectl apply -f -

- name: Create local persistent volume
  shell: kubectl apply --namespace openldap -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/openldap/templates/openldap-pv-pvc.yml')}}"

- name: Add helm repo
  shell: helm repo add helm-openldap https://jp-gouin.github.io/helm-openldap/

- name: Update helm repo
  shell: helm repo update

- name: Deploy openldap
  shell: helm install openldap --namespace openldap -f - helm-openldap/openldap-stack-ha --version 3.0.2
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/openldap/templates/openldap-overrides.yml')}}"

- name: Setup openldap LoadBalancer service
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/openldap/templates/openldap-loadbalancer-service.yml')}}"

# Do not run this on prod because it will override certificate to lets encrypt staging
- name: Setup ingress
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/openldap/templates/openldap-ingress.yml')}}"

- pause:
    seconds: 40

- name: Initial olc setup
  shell: |
    kubectl exec -n openldap openldap-0 -- sh -c "ldapmodify -Y EXTERNAL -H ldapi:// <<!
    {{lookup('template', '{{apps_dir}}/openldap/templates/{{item}}.ldif')}}!
    "
  with_items:
    - 05-initial-olc-setup
    - 08-setup-alc

- name: Create org structure
  shell: |
    kubectl exec -n openldap openldap-0 -- sh -c "ldapmodify -x -D 'cn={{ldap_manage_user}},{{top_dn}}' -w {{ldap_manage_user_password}} -H ldaps:// <<!
    {{lookup('template', '{{apps_dir}}/openldap/templates/10-create-org-structure.ldif')}}!
   "
