apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: openldap-ingress
  namespace: openldap
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-staging
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - ldap.{{domain}}
        - ldap-admin.{{domain}}
      secretName: {{openldap_tls_secret_name}}
  rules:
    - host: ldap.{{domain}}
    - host: ldap-admin.{{domain}}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: openldap-phpldapadmin
                port:
                  number: 80
