global:
  ldapDomain: {{domain}}
  adminPassword: {{ldap_admin_password}}
  configPassword: {{ldap_admin_password}}
replicaCount: 1
customTLS:
  enabled: true
  secret: {{openldap_tls_secret_name}}
  CA:
    enabled: true
replication:
  enabled: false
ltb-passwd:
  enabled: false
phpldapadmin:
  enabled: true
  ingress:
    enabled: false
  env:
    PHPLDAPADMIN_LDAP_CLIENT_TLS_REQCERT: "never"
persistence:
  enabled: true
  existingClaim: openldap-pvc
