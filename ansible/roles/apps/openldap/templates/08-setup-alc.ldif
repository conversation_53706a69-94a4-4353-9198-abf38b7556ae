dn: olcDatabase={1}mdb,cn=config
changetype: modify
replace: olcAccess
olcAccess: to *
  by dn.exact=gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth manage
  by * break
olcAccess: to attrs=altServer,namingContexts,supportedControl,supportedExtension,supportedFeatures,supportedLdapVersion,supportedSASLMechanisms
  {{default_access}}
olcAccess: to attrs=userPassword,shadowLastChange
  by dn=\"cn=admin,{{top_dn}}\" write
  by anonymous auth
  by * none
olcAccess: to dn.base=\"{{people_dn}}\"
  by dn=\"cn=vpnauth,ou=Services,{{top_dn}}\" search
  by dn=\"cn=redmineauth,ou=Services,{{top_dn}}\" search
  by group/groupOfUniqueNames/uniqueMember=\"cn=UserCreator,{{roles_dn}}\" write
  {{default_access}}
olcAccess: to dn.one=\"{{people_dn}}\"
  by group/groupOfUniqueNames/uniqueMember=\"cn=UserCreator,{{roles_dn}}\" write
  by dn=\"cn=vpnauth,ou=Services,{{top_dn}}\" read
  by dn=\"cn=redmineauth,ou=Services,{{top_dn}}\" read
  {{default_access}}
olcAccess: to *
  {{default_access}}
