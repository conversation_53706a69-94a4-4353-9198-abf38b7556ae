dn: {{people_dn}}
changetype: add
objectClass: organizationalUnit
ou: People

dn: ou=Services,{{top_dn}}
changetype: add
objectClass: organizationalUnit
ou: Services

dn: {{groups_dn}}
changetype: add
objectClass: organizationalUnit
ou: Groups

dn: cn=posixadmin,{{groups_dn}}
changetype: add
objectclass: posixGroup
cn: posixadmin
gidnumber: 500

dn: cn=posixuser,{{groups_dn}}
changetype: add
objectclass: posixGroup
cn: posixuser
gidnumber: 4501

dn: {{roles_dn}}
changetype: add
objectClass: organizationalUnit
ou: Roles

dn: cn=UserCreator,{{roles_dn}}
changetype: add
objectclass: groupOfUniqueNames
cn: UserCreator
uniqueMember: cn=tolik.l,{{people_dn}}
