apiVersion: v1
kind: Service
metadata:
  name: openldap-loadbalancer
  namespace: openldap
  labels:
    app.kubernetes.io/component: openldap
    release: openldap
spec:
  ports:
    - name: ldap-port
      protocol: TCP
      port: 389
      targetPort: ldap-port
    - name: ssl-ldap-port
      protocol: TCP
      port: 636
      targetPort: ssl-ldap-port
  selector:
    app.kubernetes.io/component: openldap
    release: openldap
  type: LoadBalancer
  loadBalancerIP: {{openldap_ip}}
