apiVersion: v1
kind: PersistentVolume
metadata:
  name: openldap-pv
  labels:
    app: openldap
spec:
  capacity:
    storage: {{volume_s}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: {{cluster_data_dir}}/openldap-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: openldap-pvc
    namespace: openldap

---

kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: openldap-pvc
  namespace: openldap
  labels:
    app: openldap
spec:
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{volume_s}}
  storageClassName: local-storage
  selector:
    matchLabels:
      app: openldap
