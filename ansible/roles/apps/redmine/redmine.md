# Manual parts

Before running the playbook if not exists create user in pgAdmin with default settings called bn_redmine (set Can log in? to true) and bitnami_redmine database, make bn_redmine user owner of that db.

## Configure ldap auth

After installing login as admin, go to Administration > LDAP authentication. Values:

Name: ldap
Host: openldap.openldap.svc.cluster.local
Port: 636 LDAPS (without certificate check)
Account: cn=redmineauth,ou=Services,dc=fernir,dc=co
Password: <<redmineauth password>>
Base DN: ou=People,dc=fernir,dc=co
LDAP filter: (memberOf=cn=redmine,ou=Groups,dc=fernir,dc=co)
On-the-fly user creation: checked

Login attribute: uid
Firstname attribute: givenname
Lastname attribute:sn
Email attribute: mail

## Delete

helm uninstall -n redmine redmine
kubectl debug node/talos-ucr-oz8 -n debug -it --image=busybox
rm -rf /host/var/mnt/cluster-data/redmine-pv
mkdir -p /host/var/mnt/cluster-data/redmine-pv
cd /host/var/mnt/cluster-data/redmine-pv

delete ingress and pv pvc

## Secrets

kubectl create secret generic -n redmine redmine-creds --from-literal=redmine-password='password'

kubectl create secret generic -n redmine redmine-db-creds --from-literal=user=bn_redmine --from-literal=password='password'

## Configure rclone for Cloudflare R2 Storage

https://rclone.org/s3/

https://developers.cloudflare.com/r2/examples/rclone/

1. Install rclone by running the following command in your terminal:
sudo -v ; curl https://rclone.org/install.sh | sudo bash

1. Generate an Access Admin Key by visiting: https://developers.cloudflare.com/r2/api/s3/tokens/
2. Configure rclone by running the following command in your terminal:
rclone config

- Select n for a new remote.
- Enter a name for the remote (e.g., r2demo).
- Choose 5 for Cloudflare as the storage type.
- Select 6 for Cloudflare R2 Storage.
- Choose 1 to enter AWS credentials.
- Set the access_key_id and secret_access_key obtained from the generated token.
- Press [Enter] to leave the region empty.
- Choose the default endpoint provided by the token.
- Select n to not edit advanced config.
- Confirm to keep the remote configuration.

## How to download a file from cloudflare

1. Open https://dash.cloudflare.com/ and select your account.
2. In the left sidebar, navigate to the "R2" item.
3. Click on the bucket containing the desired file.
4. Locate the file you want to download and click on the three dots on the right side.
5. Select "Download" from the options menu.

## Backup restore commands

cd /mnt/hdd/cluster-data

tar -czvf redmine.tar.gz ./redmine-pv

rclone copy redmine.tar.gz r2demo:general

In r2 allow public access, copy file url. Connect to node.

cd /host/var/mnt/cluster-data

wget <file url>

In r2 dissalow public access.

tar -xvzf redmine.tar.gz

If password for db user was changed, then need to change it in config/database.yml in redmine-pv folder

## Remove rclone

sudo rm /usr/bin/rclone /usr/local/share/man/man1/rclone.1 && sudo mandb

## Other info

https://artifacthub.io/packages/helm/bitnami/redmine
