apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: redmine-ingress
  namespace: redmine
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 15m
spec:
  ingressClassName: nginx
  rules:
    - host: redmine.{{domain}}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: redmine
                port:
                  number: 80
