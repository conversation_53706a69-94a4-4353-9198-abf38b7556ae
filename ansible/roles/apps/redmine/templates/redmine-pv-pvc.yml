apiVersion: v1
kind: PersistentVolume
metadata:
  name: redmine-pv
  labels:
    app: redmine
spec:
  capacity:
    storage: {{volume_s}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: "{{cluster_data_dir}}/redmine-pv"
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: redmine-pvc
    namespace: redmine

---

kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: redmine-pvc
  namespace: redmine
  labels:
    app: redmine
spec:
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{volume_s}}
  storageClassName: local-storage
  selector:
    matchLabels:
      app: redmine
