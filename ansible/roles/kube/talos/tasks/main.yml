- set_fact:
    talos_km_name_dir: "{{secrets_talos_km_name_dir}}"
  when: cluster_environment == "prod"

# # use this only during development to update configs
# - name: Apply config
#   shell: talosctl apply-config -f {{talos_km_name_dir}}/controlplane.yaml

- name: Generate config
  shell: talosctl gen config {{km_name}} https://{{km_ip}}:6443

# After this step move generated files to templates/{{km_name}}. For prod put it into {{secrets_talos_km_name_dir}}

- name: Save talosconfig
  shell: |
    talosctl config merge {{talos_km_name_dir}}/talosconfig
    talosctl config context {{km_name}}
    talosctl config endpoint {{km_ip}}
    talosctl config node {{km_ip}}

# prod: through VNC configure F3 networking on km
# press Enter or Tab to go to next field
# hostname: {{km_name}}
# dns servers: *******,*******
# time servers: <leave empty>
# interface: select first interface, not "none" option. Press Tab to go to next field
# mode: static
# addresses: {{km_ip}}/24
# Gateway: <usually {{km_ip}} with 1 replaced as the last number>

# prod: vm will reboot after this

# prod: run talosctl disks --insecure -n ************** to see disk name and put it into controlplane.yaml machine.install.disk section.

# if your controlplane.yaml does not yet have our customizations, then apply them. See our exsitsng controlplane.yaml files and look
# for # custom ============================== comments

# controlplane.yaml is manually customized, it does not take variables from ansible, need to change variables by hand, for example
# ip ************** is hardcoded there
- name: Apply insecure config
  shell: talosctl apply-config --insecure --nodes {{km_ip}} -f {{talos_km_name_dir}}/controlplane.yaml

# prdo: maybe vm will reboot after this

- name: Bootstrap
  shell: talosctl bootstrap

- name: Extract kube config
  shell: talosctl kubeconfig --nodes {{km_ip}} --endpoints {{km_ip}}

# not sure if need to wait for reboot here. May be need to reboot for allowSchedulingOnControlPlanes: true to apply

- pause:
    seconds: 30

# yml has customization inside
- name: Deploy kubernetes dashboard into cluster
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{ lookup('file', '{{talos_templates_dir}}/custom-kubernetes-dashboard.yml')}}"

- name: Create dashboard service account
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{ lookup('file', '{{talos_templates_dir}}/dashboard-admin-sa.yml')}}"





