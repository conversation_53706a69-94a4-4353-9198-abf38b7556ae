# Helpful commands

talosctl dashboard

talosctl disks

talosctl list var/mnt/cluster-data -d 2

talosctl reset --system-labels-to-wipe EPHEMERAL

# Local talos install

You can understand how to install talos locally by following our test task. No need to record and upload a video:

https://docs.google.com/document/d/1VKig-PvQ9wPz3_xzdP_aBYFK5weyj9PuAttLIl_Irm0/edit?usp=sharing

# Upgrading talos

Note: when using EPHEMERAL Talos partition (/var), make sure to use --preserve set while performing upgrades, otherwise you risk losing data.

make sure to use --preserve

# Networking

t get nftableschain -o yaml

nmap **************

nmap -p 80,443,31003 **************

nmap -p 31003 **************

Default pod subnet: **********/16
Default service subnet: *********/12

https://taloscommunity.slack.com/archives/CMARMBC4E/p1708001324748919

# Image

https://github.com/siderolabs/talos/releases/download/v1.6.5/metal-amd64.iso

## Kernel paramenters

ip=<client-ip>:<server-ip>:<gw-ip>:<netmask>:<hostname>:<device>:<autoconf>:<dns0-ip>:<dns1-ip>:<ntp0-ip>

ip=**************::*************:*************::::*******:*******

network interface: enx5254002f4f72

https://jarvis.stark-industries.solutions/


# Unfinished alternative way to apply config patches

shell: talosctl  patch mc -p @{{talos_templates_dir}}/controlplane-overrides.yml

