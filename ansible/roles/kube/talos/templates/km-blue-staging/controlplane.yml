version: v1alpha1 # Indicates the schema used to decode the contents.
debug: false # Enable verbose logging to the console.
persist: true
# Provides machine specific configuration options.
machine:
    type: controlplane # Defines the role of the machine within the cluster.
    token: dy7gn1.gp96726r8lgnrl6b # The `token` is used by a machine to join the PKI of the cluster.
    # The root certificate authority of the PKI.
    ca:
        crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJQekNCOHFBREFnRUNBaEVBN0lveVFUbWI2Zkg4dC9XeVZIR2ZiREFGQmdNclpYQXdFREVPTUF3R0ExVUUKQ2hNRmRHRnNiM013SGhjTk1qUXdNakU0TURZeE16TTRXaGNOTXpRd01qRTFNRFl4TXpNNFdqQVFNUTR3REFZRApWUVFLRXdWMFlXeHZjekFxTUFVR0F5dGxjQU1oQUhCb0lvUzFSU3kvNDNDaXZSMkh1Mjh5dGJpaHVZbHF5TEpoCnZrWURTdWl5bzJFd1h6QU9CZ05WSFE4QkFmOEVCQU1DQW9Rd0hRWURWUjBsQkJZd0ZBWUlLd1lCQlFVSEF3RUcKQ0NzR0FRVUZCd01DTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkFzNVRyb2MxdFAxZXdmWQpaWDRoZGJ2YU9hWTFNQVVHQXl0bGNBTkJBS0lIVmtVS1RPTnU2VHgyWXllRVN1bWN5azlUNk1TSWEvY2dGLzVmCmF3S1ZYME1qTTA2TWg2Sy9ZeUtzaEZwalNHaW5MQjViOVZlV1JaR1RWSzZUZFFBPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        key: LS0tLS1CRUdJTiBFRDI1NTE5IFBSSVZBVEUgS0VZLS0tLS0KTUM0Q0FRQXdCUVlESzJWd0JDSUVJSlhBQmpTcDRpQ1JTeEs5L3JFV0JjYWNTVmZGbldPZFFXY1NhZm9Md1dLbwotLS0tLUVORCBFRDI1NTE5IFBSSVZBVEUgS0VZLS0tLS0K
    # Extra certificate subject alternative names for the machine's certificate.
    certSANs: []

    # Used to provide additional options to the kubelet.
    kubelet:
        image: ghcr.io/siderolabs/kubelet:v1.29.0 # The `image` field is an optional reference to an alternative kubelet image.
        defaultRuntimeSeccompProfileEnabled: true # Enable container runtime default Seccomp profile.
        disableManifestsDirectory: true # The `disableManifestsDirectory` field configures the kubelet to get static pod manifests from the /etc/kubernetes/manifests directory.
        # custom ====================================================================================================================================================================================
        extraMounts:
            - destination: /var/mnt/cluster-data/postgres-pv
              type: bind
              source: /var/mnt/cluster-data/postgres-pv
              options:
                  - bind
                  - rshared
                  - rw
            - destination: /var/mnt/cluster-data/docker-registry-pv
              type: bind
              source: /var/mnt/cluster-data/docker-registry-pv
              options:
                  - bind
                  - rshared
                  - rw
            - destination: /var/mnt/cluster-data/ks-web-prod-pv
              type: bind
              source: /var/mnt/cluster-data/ks-web-prod-pv
              options:
                  - bind
                  - rshared
                  - rw
            - destination: /var/mnt/cluster-data/ks-web-staging-pv
              type: bind
              source: /var/mnt/cluster-data/ks-web-staging-pv
              options:
                  - bind
                  - rshared
                  - rw
            - destination: /var/mnt/cluster-data/ks-static-file-server-pv
              type: bind
              source: /var/mnt/cluster-data/ks-static-file-server-pv
              options:
                  - bind
                  - rshared
                  - rw
            - destination: /var/mnt/cluster-data/redmine-pv
              type: bind
              source: /var/mnt/cluster-data/redmine-pv
              options:
                  - bind
                  - rshared
                  - rw

    # Provides machine specific network configuration options.
    network: {}

    # Used to provide instructions for installations.
    install:
        disk: /dev/sda # The disk used for installations.
        image: ghcr.io/siderolabs/installer:v1.6.2 # Allows for supplying the image used to perform the installation.
        wipe: false # Indicates if the installation disk should be wiped at installation time.

    # Features describe individual Talos features that can be switched on or off.
    features:
        rbac: true # Enable role-based access control (RBAC).
        stableHostname: true # Enable stable default hostname.
        apidCheckExtKeyUsage: true # Enable checks for extended key usage of client certificates in apid.
        diskQuotaSupport: true # Enable XFS project quota support for EPHEMERAL partition and user disks.
        # KubePrism - local proxy/load balancer on defined port that will distribute
        kubePrism:
            enabled: true # Enable KubePrism support - will start local load balacing proxy.
            port: 7445 # KubePrism port.

# Provides cluster specific configuration options.
cluster:
    id: FY1qaqcBYH_JuHNZD9H5w_HxHGsob_TqfuAgv61uOrI= # Globally unique identifier for this cluster (base64 encoded random 32 bytes).
    secret: h6LdUDHgJz193fMKcr0l+B9OQL6sTSypG9v9c0b3gPU= # Shared secret of cluster (base64 encoded random 32 bytes).
    # customization =================================================================================================================================================================================
    allowSchedulingOnControlPlanes: true
    # Provides control plane specific configuration options.
    controlPlane:
        endpoint: https://*************:6443 # Endpoint is the canonical controlplane endpoint, which can be an IP address or a DNS hostname.
    clusterName: talos-vbox-cluster # Configures the cluster's name.
    # Provides cluster specific network configuration options.
    network:
        dnsDomain: cluster.local # The domain used by Kubernetes DNS.
        # The pod subnet CIDR.
        podSubnets:
            - **********/16
        # The service subnet CIDR.
        serviceSubnets:
            - *********/12
    token: upjwhg.yhdtj35fv7ww54cm # The [bootstrap token](https://kubernetes.io/docs/reference/access-authn-authz/bootstrap-tokens/) used to join the cluster.
    secretboxEncryptionSecret: Csfr6t+d9ea6BmOnhWQ66FoKSPp2/ObM2WXayag+kDM= # A key used for the [encryption of secret data at rest](https://kubernetes.io/docs/tasks/administer-cluster/encrypt-data/).
    # The base64 encoded root certificate authority used by Kubernetes.
    ca:
        crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJpakNDQVRDZ0F3SUJBZ0lSQU9vNlJIUUw4OW1keDlXS0dEVmZMNUl3Q2dZSUtvWkl6ajBFQXdJd0ZURVQKTUJFR0ExVUVDaE1LYTNWaVpYSnVaWFJsY3pBZUZ3MHlOREF5TVRnd05qRXpNemhhRncwek5EQXlNVFV3TmpFegpNemhhTUJVeEV6QVJCZ05WQkFvVENtdDFZbVZ5Ym1WMFpYTXdXVEFUQmdjcWhrak9QUUlCQmdncWhrak9QUU1CCkJ3TkNBQVJRWklZZWF1K3B0b2VuZmxNaHBlZGhPZFR0WFJiVFJrZStRbnJRV2lVVGpoRFpYR1QxTHd1bDB5OWEKcWROcGpDR3U4NCsrcnlzZ1IzZnA2V3FoRWtnd28yRXdYekFPQmdOVkhROEJBZjhFQkFNQ0FvUXdIUVlEVlIwbApCQll3RkFZSUt3WUJCUVVIQXdFR0NDc0dBUVVGQndNQ01BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0hRWURWUjBPCkJCWUVGRTYybUZBSEwvMUVqT05Dd2g3Tmp3anBIV3NpTUFvR0NDcUdTTTQ5QkFNQ0EwZ0FNRVVDSUZCVmVKeHgKMWdldjA0OFdLVWZvWkRkQ0VEekUzaFd3QjRQRUtwQm5lQ08xQWlFQTl5MHBNQlF5cngxb3lIRVBGN0F4d0lKSQptZEhBcTd0ZVNsalFJMmhxeFJvPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        key: ****************************************************************************************************************************************************************************************************************************************************************************************************************
    # The base64 encoded aggregator certificate authority used by Kubernetes for front-proxy certificate generation.
    aggregatorCA:
        crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJYakNDQVFXZ0F3SUJBZ0lRY05PMU41OHVRU3Jabm96d1NQVWQ1ekFLQmdncWhrak9QUVFEQWpBQU1CNFgKRFRJME1ESXhPREEyTVRNek9Gb1hEVE0wTURJeE5UQTJNVE16T0Zvd0FEQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxRwpTTTQ5QXdFSEEwSUFCTXh3OVhGRGVwNVA4c3loMXRReW56K3l0Wnl6TXovN2ZwOVQwOEI4eTBBK2N1YUZ0YXU3CkI3TFJWM3NlSEI4Y0tPc0dyaEhWcVl0MVFua3ljb0FyUXBtallUQmZNQTRHQTFVZER3RUIvd1FFQXdJQ2hEQWQKQmdOVkhTVUVGakFVQmdnckJnRUZCUWNEQVFZSUt3WUJCUVVIQXdJd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZApCZ05WSFE0RUZnUVVlT0VTSTdCS1Y3NHVmSWJYS3lSaFlkSDl5c1V3Q2dZSUtvWkl6ajBFQXdJRFJ3QXdSQUlnCks5bmhkV3VQcXU1em5jTmxJRWVsLzBCMlR6emtZT2RNWG45WFYrTFFkNGtDSUdUeGFGTTE2Y1NRam5zVC8rRU0KalNTU2xiYjdOcVgrbnRJelpwTW5vcm1OCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
        key: ****************************************************************************************************************************************************************************************************************************************************************************************************************
    # The base64 encoded private key for service account token generation.
    serviceAccount:
        key: ****************************************************************************************************************************************************************************************************************************************************************************************************************
    # API server specific configuration options.
    apiServer:
        image: registry.k8s.io/kube-apiserver:v1.29.0 # The container image used in the API server manifest.
        # Extra certificate subject alternative names for the API server's certificate.
        certSANs:
            - *************
        disablePodSecurityPolicy: true # Disable PodSecurityPolicy in the API server and default manifests.
        # Configure the API server admission plugins.
        admissionControl:
            - name: PodSecurity # Name is the name of the admission controller.
              # Configuration is an embedded configuration object to be used as the plugin's
              configuration:
                apiVersion: pod-security.admission.config.k8s.io/v1alpha1
                defaults:
                    audit: restricted
                    audit-version: latest
                    enforce: baseline
                    enforce-version: latest
                    warn: restricted
                    warn-version: latest
                exemptions:
                    namespaces:
                        - kube-system
                    runtimeClasses: []
                    usernames: []
                kind: PodSecurityConfiguration
        # Configure the API server audit policy.
        auditPolicy:
            apiVersion: audit.k8s.io/v1
            kind: Policy
            rules:
                - level: Metadata
    # Controller manager server specific configuration options.
    controllerManager:
        image: registry.k8s.io/kube-controller-manager:v1.29.0 # The container image used in the controller manager manifest.
    # Kube-proxy server-specific configuration options
    proxy:
        image: registry.k8s.io/kube-proxy:v1.29.0 # The container image used in the kube-proxy manifest.

        # # Disable kube-proxy deployment on cluster bootstrap.
        # disabled: false
    # Scheduler server specific configuration options.
    scheduler:
        image: registry.k8s.io/kube-scheduler:v1.29.0 # The container image used in the scheduler manifest.
    # Configures cluster member discovery.
    discovery:
        enabled: true # Enable the cluster membership discovery feature.
        # Configure registries used for cluster member discovery.
        registries:
            # Kubernetes registry uses Kubernetes API server to discover cluster members and stores additional information
            kubernetes:
                disabled: true # Disable Kubernetes discovery registry.
            # Service registry is using an external service to push and pull information about cluster members.
            service: {}
            # # External service endpoint.
            # endpoint: https://discovery.talos.dev/
    # Etcd specific configuration options.
    etcd:
        # The `ca` is the root certificate authority of the PKI.
        ca:
            crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJmVENDQVNTZ0F3SUJBZ0lSQVBMSCtSYmVJc3VXWEhQOUw0VGgxMHN3Q2dZSUtvWkl6ajBFQXdJd0R6RU4KTUFzR0ExVUVDaE1FWlhSalpEQWVGdzB5TkRBeU1UZ3dOakV6TXpoYUZ3MHpOREF5TVRVd05qRXpNemhhTUE4eApEVEFMQmdOVkJBb1RCR1YwWTJRd1dUQVRCZ2NxaGtqT1BRSUJCZ2dxaGtqT1BRTUJCd05DQUFSRjg1Y0drSWFSClV5TVljMWFMTk1KNWlSVVE1TFFyOHIxbUZsWnFiNnkyUGluVTdnRFB6WkxjSUtpd0IxMUFYQ04xVE5yb1ppc0IKRjFITkVqUWZqRkRYbzJFd1h6QU9CZ05WSFE4QkFmOEVCQU1DQW9Rd0hRWURWUjBsQkJZd0ZBWUlLd1lCQlFVSApBd0VHQ0NzR0FRVUZCd01DTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkVleS9MS0VzbFJpCmxPdEgyaUd4Z1VlWGc2Y0lNQW9HQ0NxR1NNNDlCQU1DQTBjQU1FUUNJSDZrWHR1cFI1NG9RWHBaVmkyWlE3cGcKN2IyZmVjYXlxY2JpMTBuVmV5RjdBaUJOYnBPTTYxcjlOQlFweDNEUUJJSUlTNGt0M2hlRXBmSkdrSzZUSTNxbAo5Zz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
            key: ****************************************************************************************************************************************************************************************************************************************************************************************************************

    # A list of urls that point to additional manifests.
    extraManifests: []
    # A list of inline Kubernetes manifests.
    inlineManifests: []

---
# custom ==============================================================================================================================================================================================
apiVersion: v1alpha1
kind: NetworkDefaultActionConfig
ingress: block
---
apiVersion: v1alpha1
kind: NetworkRuleConfig
name: kubelet-ingress
portSelector:
  ports:
    - 10250
  protocol: tcp
ingress:
  - subnet: *************/32
---
apiVersion: v1alpha1
kind: NetworkRuleConfig
name: apid-ingress
portSelector:
  ports:
    - 50000
  protocol: tcp
ingress:
  - subnet: 0.0.0.0/0
  - subnet: ::/0
---
apiVersion: v1alpha1
kind: NetworkRuleConfig
name: trustd-ingress
portSelector:
  ports:
    - 50001
  protocol: tcp
ingress:
  - subnet: *************/32
---
apiVersion: v1alpha1
kind: NetworkRuleConfig
name: kubernetes-api-ingress
portSelector:
  ports:
    - 6443
  protocol: tcp
ingress:
  - subnet: 0.0.0.0/0
  - subnet: ::/0
---
apiVersion: v1alpha1
kind: NetworkRuleConfig
name: etcd-ingress
portSelector:
  ports:
    - 2379-2380
  protocol: tcp
ingress:
  - subnet: *************/32
---
apiVersion: v1alpha1
kind: NetworkRuleConfig
name: cni-vxlan
portSelector:
  ports:
    - 4789
  protocol: udp
ingress:
  - subnet: *************/32
