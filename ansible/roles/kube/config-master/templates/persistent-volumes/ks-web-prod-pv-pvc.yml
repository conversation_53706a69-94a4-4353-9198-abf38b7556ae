---
kind: PersistentVolume
apiVersion: v1
metadata:
  name: ks-web-prod-pv
  labels:
    app: ks-web-prod
spec:
  storageClassName: local-storage
  capacity:
    storage: {{volume_xxxl}}
  accessModes:
    - ReadWriteOnce
  local:
    path: {{cluster_data_dir}}/ks-web-prod-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: ks-web-prod-pvc
    namespace: ks-prod

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app: ks-web-prod
  name: ks-web-prod-pvc
  namespace: ks-prod
spec:
  storageClassName: local-storage
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: {{volume_xxxl}}
