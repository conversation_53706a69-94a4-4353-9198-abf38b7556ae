---
kind: PersistentVolume
apiVersion: v1
metadata:
  name: ks-static-file-server-pv
  namespace: default
  labels:
    app: ks-static-file-server
spec:
  storageClassName: local-storage
  capacity:
    storage: {{volume_xs}}
  accessModes:
    - ReadWriteOnce
  local:
    path: {{cluster_data_dir}}/ks-static-file-server-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: ks-static-file-server-pvc
    namespace: static-file-server

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app: ks-static-file-server
  name: ks-static-file-server-pvc
  namespace: static-file-server
spec:
  storageClassName: local-storage
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: {{volume_xs}}
