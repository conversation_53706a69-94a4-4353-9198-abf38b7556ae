- name: Create namespace {{item}}
  shell: kube<PERSON>l create namespace {{item}}
  loop: ["ks-prod", "static-file-server", "postgres", "ks-staging"]

- name: Create cluster-data directory
  become: yes
  file:
    path: "{{cluster_data_dir}}"
    state: directory
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"

- name: Create fast-cluster-data directory
  become: yes
  file:
    path: "{{fast_cluster_data_dir}}"
    state: directory
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"

- name: Create local pv directory
  file:
    path: "{{cluster_data_dir}}/local-pv"
    state: directory

- name: Create local pv 2 directory
  file:
    path: "{{cluster_data_dir}}/local-pv2"
    state: directory

- name: Create ks-web pv directory
  file:
    path: "{{cluster_data_dir}}/ks-web-pv"
    state: directory

- name: Create ks-web-prod pv directory
  file:
    path: "{{cluster_data_dir}}/ks-web-prod-pv"
    state: directory

- name: Create ks-web-staging pv directory
  file:
    path: "{{cluster_data_dir}}/ks-web-staging-pv"
    state: directory

- name: Create ks-static-file-server pv directory
  file:
    path: "{{cluster_data_dir}}/ks-static-file-server-pv"
    state: directory

- name: Create postgresql-secret in namespace {{item}}
  shell: kubectl create secret generic postgresql-secret --namespace={{item}} --from-literal=postgres-password={{postgresql_password_dev}}
  loop: ["postgres", "ks-prod", "ks-staging"]
  ignore_errors: true

- name: Create priority classes
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/priority-classes.yml')}}"

- name: Create local storage class
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('file', '{{kub_config_master_templates_dir}}/persistent-volumes/local-storage-sc.yml')}}"

- name: Create local persistent volume
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/local-pv.yml')}}"

- name: Create local persistent volume 2
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/local-pv2.yml')}}"

- name: Create ks-web pv and pvc
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/ks-web-pv-pvc.yml')}}"

- name: Create ks-web-prod pv and pvc
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/ks-web-prod-pv-pvc.yml')}}"

- name: Create ks-web-staging pv and pvc
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/ks-web-staging-pv-pvc.yml')}}"

- name: Create ks-static-file-server pv and pvc
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/ks-static-file-server-pv-pvc.yml')}}"

