- name: Install packages
  become: yes
  apt:
    name: "{{packages}}"
    state: present
    update_cache: yes
  vars:
    packages:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common

- name: Add Docker APT GPG key
  become: yes
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg

- name: Add Docker APT repository
  become: yes
  apt_repository:
    repo: "deb https://download.docker.com/linux/ubuntu focal stable"
    state: present
    filename: 'docker'

- name: Install containerd
  become: yes
  apt:
    update_cache: yes
    name: "containerd.io=1.6.8-1*"
    state: present

- name: Hold containerd version
  become: yes
  dpkg_selections:
    name: containerd.io
    selection: hold

- name: Create containerd config pv directory
  become: yes
  file:
    path: "/etc/containerd"
    state: directory

- name: Create registry directory
  become: yes
  file:
    path: "/etc/containerd/certs.d/{{registry}}"
    state: directory

- name: Generate config
  become: yes
  shell: containerd config default>/etc/containerd/config.toml

- name: Configure registry config file
  become: yes
  replace:
    path: /etc/containerd/config.toml
    regexp: 'config_path = ""'
    replace: 'config_path = "/etc/containerd/certs.d"'

- name: Copy host.toml template
  become: yes
  template:
    src: "{{containerd_templates_dir}}/hosts.toml"
    dest: "/etc/containerd/certs.d/{{registry}}/hosts.toml"

- name: Restart containerd service and enable on startup
  become: yes
  service:
    name: containerd
    state: restarted
    enabled: yes
