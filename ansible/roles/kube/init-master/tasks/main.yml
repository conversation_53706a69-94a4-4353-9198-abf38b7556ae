- name: Template Kubernetes ClusterConfig
  become: yes
  template:
    src: "{{ kube_init_master_dir }}/templates/kubeadm-config.yml"
    dest: "/tmp/kubeadm-config-temp.yml"

- name: Set ip forwarding on in /proc and verify token value
  become: yes
  sysctl:
    name: net.ipv4.ip_forward
    value: '1'
    sysctl_set: yes

# todo fd sec security hardcoded cert extra sans
- name: Init Kubernetes cluster
  become: yes
  shell: kubeadm init --config "/tmp/kubeadm-config-temp.yml" --v=5 --ignore-preflight-errors=NumCPU

  # todo fd sec security is this secure?
- name: Create dashboard admin service account
  become: yes
  command: |
    kubectl --kubeconfig={{ kubeadmin_config }} apply -f -
  register: sa_create_result
  until: sa_create_result.rc == 0
  retries: 10
  delay: 5
  args:
    stdin: "{{ lookup('file', '{{kube_init_master_dir}}/templates/dashboard-admin-sa.yml') }}"

- name: Create Kubernetes config directory for root
  become: yes
  file:
    path: "/root/.kube/"
    state: directory
    mode: 0770

- name: Copy admin.conf to Home directory for root
  become: yes
  copy:
    src: "{{ kubeadmin_config }}"
    dest: "/root/.kube/config"
    mode: 0660
    remote_src: true

- name: Create Kubernetes config directory
  file:
    path: "/home/<USER>/.kube/"
    state: directory
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"
    mode: 0700

- name: Copy config to Home directory
  become: yes
  copy:
    src: "{{ kubeadmin_config }}"
    dest: "/home/<USER>/.kube/config"
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"
    mode: 0600
    remote_src: true

- name: Create Kubernetes config directory in sources
  become: yes
  file:
    path: "{{sources_dir}}/.kube/"
    state: directory
    mode: 0700

- name: Copy admin.conf to sources directory
  become: yes
  copy:
    src: "{{ kubeadmin_config }}"
    dest: "{{sources_dir}}/.kube/config"
    mode: 0600
    remote_src: true

- name: Untaint master
  command: kubectl taint nodes --all node-role.kubernetes.io/control-plane-

# yml has customization inside
- name: Deploy kubernetes dashboard into cluster
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('file', '{{kube_init_master_dir}}/templates/kubernetes-dashboard.yml')}}"
  register: create_result
  until: create_result.rc == 0
  retries: 4
  delay: 6

