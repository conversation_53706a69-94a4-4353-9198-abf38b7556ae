- name: Add ingress-nginx repo
  shell: helm repo add --kube-context={{kubectl_context}} ingress-nginx https://kubernetes.github.io/ingress-nginx

- name: Install ingress-nginx
  shell: helm install ingress-nginx --kube-context={{kubectl_context}} --namespace ingress-nginx --create-namespace -f - ingress-nginx/ingress-nginx --version 4.12.0
  args:
    stdin: "{{ lookup('template', '{{ingress_nginx_templates_dir}}/ingress-nginx-overrides.yml')}}"

- pause:
    seconds: 30

- name: Setup ks web ingress
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{ lookup('template', '{{ingress_nginx_templates_dir}}/ks-web-ingress.yml')}}"
