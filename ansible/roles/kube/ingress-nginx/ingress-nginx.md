# In case you want to have multpiple load balancers

Edit ingress service and add
  if cluster_environment is "prod" : loadBalancerIP: **********
  if cluster_environment is "staging": loadBalancerIP: ********** or value of km_ip
  if cluster_environment is "local": loadBalancerIP: **********

# Delete

helm delete -n ingress-nginx ingress-nginx

# Static ip

************

# Tcp

https://stackoverflow.com/questions/61430311/exposing-multiple-tcp-udp-services-using-a-single-loadbalancer-on-k8s

https://stackoverflow.com/questions/69802098/nginx-ingress-helm-deployment-tcp-services-configmap-argument-not-found

https://kubernetes.github.io/ingress-nginx/user-guide/exposing-tcp-udp-services/

psql -U postgres -p 31003 -h fernir.biz

ingress-nginx-controller.ingress-nginx.svc.cluster.local


