apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ks-web-{{cluster_environment}}-ingress
  namespace: ks-{{cluster_environment}}
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 15m
spec:
  ingressClassName: nginx
  rules:
    - host: app.{{domain}}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ks-web-{{cluster_environment}}
                port:
                  number: 8080

