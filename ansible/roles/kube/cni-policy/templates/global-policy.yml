# https://projectcalico.docs.tigera.io/security/tutorials/protect-hosts

apiVersion: projectcalico.org/v3
kind: HostEndpoint
metadata:
  name: vpsgtmt0.s-host.host-eth0
  labels:
    host-endpoint: ingress
spec:
  interfaceName: eth0
  node: vpsgtmt0.s-host.host

---

apiVersion: projectcalico.org/v3
kind: HostEndpoint
metadata:
  name: vpsgtmt0.s-host.host-tun0
  labels:
    host-endpoint: ingress
spec:
  interfaceName: tun0
  node: vpsgtmt0.s-host.host

---

apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: allow-cluster-internal-ingress
spec:
  order: 10
  preDNAT: true
  applyOnForward: true
  ingress:
    - action: Allow
      source:
        nets: [**********/16, *********/12]
  selector: has(host-endpoint)

---

apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: drop-other-ingress
spec:
  order: 20
  preDNAT: true
  applyOnForward: true
  ingress:
    - action: Deny
  selector: has(host-endpoint)

---

apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: allow-outbound-external
spec:
  order: 10
  egress:
    - action: Allow
  selector: has(host-endpoint)

---

apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: allow-nodeports
spec:
  preDNAT: true
  applyOnForward: true
  order: 10
  ingress:
    - action: Allow
      protocol: TCP
      destination:
        ports: [1194]
  selector: has(host-endpoint)

---

apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: allow-nodeports-from-our-vpn
spec:
  preDNAT: true
  applyOnForward: true
  order: 12
  ingress:
    - action: Allow
      protocol: TCP
      destination:
        ports: [443,31004,31007,5900]
      source:
        nets:
          - {{vpn_network_cidr}}
  selector: has(host-endpoint)

---

apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: allow-additional-nodeports-from-admin-vpn
spec:
  preDNAT: true
  applyOnForward: true
  order: 11
  ingress:
    - action: Allow
      protocol: TCP
      destination:
        ports: [443,30500,31003,31004,31007,30904,30905,31009]
      source:
        nets:
          - {{vpn_network_admin_cidr}}
          - **************/32
  selector: has(host-endpoint)

---

#todo fd sec 1 port 80 is open to outside
apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: allow-port-80-for-lets-encrypt-acme-challenges
spec:
  selector: has(host-endpoint)
  preDNAT: true
  applyOnForward: true
  order: 12
  ingress:
    - action: Allow
      protocol: TCP
      destination:
        ports: [80]
