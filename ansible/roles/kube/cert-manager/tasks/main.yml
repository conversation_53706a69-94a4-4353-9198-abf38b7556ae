- name: Create custom resource definitions
  shell: kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.10.0/cert-manager.crds.yaml

- name: Create namespace
  shell: kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -

- name: Add cert-manager repo
  shell: helm repo add jetstack https://charts.jetstack.io

- name: Install cert-manager
  shell: helm install cert-manager --namespace cert-manager --create-namespace -f - jetstack/cert-manager --version 1.10.0
  args:
    stdin: "{{ lookup('template', '{{kube_dir}}/cert-manager/templates/cert-manager-overrides.yml')}}"

- name: Setup cluster issuers
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('template', '{{kube_dir}}/cert-manager/templates/lets-encrypt-cluster-issuers.yml')}}"

# helm uninstall cert-manager -n cert-manager
# to remove custom resources
# kubectl delete -f https://github.com/cert-manager/cert-manager/releases/download/v1.10.0/cert-manager.crds.yaml

