# Cert-Manager and Let’s Encrypt

[How to Install Kubernetes Cert-Manager and Configure Let’s Encrypt](https://www.howtogeek.com/devops/how-to-install-kubernetes-cert-manager-and-configure-lets-encrypt/)

## Special uninstall instructions

[Uninstalling cert-manager with <PERSON><PERSON>](https://cert-manager.io/docs/installation/helm/#uninstalling-with-helm)

## Instruction for generation first certificate

Replace IP address for services, which will receive the certificate, with the IP address server's (for staging it is *************, production - *************), in the DNS settings (your hosting provider's). For example, if we need to create\update the certificate for the LDAP, then in the DNS we need to supply the server's IP address as the LDAP address, both ldap and ldap-admin.

Then we need to wait some time for all this settings saved in the DNS.

### Metallb
You need to switch to "metallb-system" namespace. Then add in the IP Address Pool (like: Custom Resource
Definitions > ipaddresspools.metallb.io > default) in field "addresses", your server's IP address with "/32" at the end.

Then, after that, as it was done, set your server's IP address in ingress-nginx LoadBalancer service as loadBalancerIP (like: loadBalancerIP: "server's IP"). See that IP in External Endpoints changed to server's IP.

### Ingress
Go to the ingress resource (for what you do certificate), in annotations, add cluster-issuer (letsencrypt-staging).

And look inside that namespace should appear pod. This pod will want to connect with letsencrypt.

They will connect if the DNS IP is pointing to the correct address.

Then this pod will deleted. You can read it logs (that all was successful).

You can also read in cert-manager logs that the certificate issuance was successful.

### Cleanup after cert generation
Restart postgres

From openldap-tls tls.crt take 2nd section with BEGIN SERTIFICATE and add it field to ca.crt.

Restart openldap.

Restart openvpn:

<NAME_EMAIL>

Return ingress controller to old loadBalancerIP.

Return IP Address Pool to old IP.

Return old DNS settings. Note that for ldap prod ip is **********. All other are **********

## How to force cert renewal

Follow guide for first cert generation. Then:

In each ingress change cert-manager.io/cluster-issuer to letsencrypt-staging then wait for successful cert
provision then change to letsencrypt-prod and wait for succesful provision.

Then follow guide for first cert generation

## Suggestion

If you’re actively developing or testing a Let’s Encrypt client, please use letsencrypt-staging. letsencrypt-prod only for production (to avoid hitting Let’s Encrypt’s stringent production rate limits).
