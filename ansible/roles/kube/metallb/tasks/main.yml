- name: Install metallb
  shell: kubectl apply --context={{kubectl_context}} -f https://raw.githubusercontent.com/metallb/metallb/v0.14.3/config/manifests/metallb-native.yaml

- name: Pause for 15 seconds
  ansible.builtin.pause:
    seconds: 15

- name: Configure metallb
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{ lookup('template', '{{metallb_templates_dir}}/metallb-config.yml')}}"
