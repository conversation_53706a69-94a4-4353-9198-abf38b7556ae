# Registry

```bash
d tag hello-world 192.168.1.50:30500/hello-world

d push 192.168.1.50:30500/hello-world

kubectl create secret docker-registry docker-hub-secret --docker-server=https://index.docker.io/v1/ --docker-username=<PERSON><PERSON><PERSON> --docker-password=#### --docker-email=<EMAIL>
```

## See docker-registry images

```bash
curl http://192.168.1.50:30500/v2/_catalog
```

## See spec docker-registry image

```bash
curl http://192.168.1.50:30500/v2/ks-web/tags/list
```

## Delete helm release

```bash
helm delete -n registry registry
```
