- name: Create pv directory for registry
  file:
    path: "{{cluster_data_dir}}/docker-registry-pv"
    state: directory

- name: Create local persistent volume
  shell: kubectl apply --namespace registry -f -
  args:
    stdin: "{{lookup('template', '{{registry_templates_dir}}/docker-registry-pv-pvc.yml')}}"

- name: Add twuni repo
  shell: helm repo add twuni https://helm.twun.io

- name: Deploy registry
  shell: helm install docker-registry --namespace registry -f - twuni/docker-registry --version 2.1.0
  args:
    stdin: "{{lookup('file', '{{registry_templates_dir}}/docker-registry-overrides.yml')}}"
