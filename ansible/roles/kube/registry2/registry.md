# Registry

d tag hello-world ************:30500/hello-world

d push ************:30500/hello-world

## See docker-registry images

curl https://************:30500/v2/_catalog
curl https://**************:30500/v2/_catalog
curl https://registry.fernir.biz:30500/v2/_catalog

## See spec docker-registry image

curl http://************:30500/v2/ks-web/tags/list
curl http://registry.fernir.biz:30500/v2/ks-web/tags/list

## Delete helm release

helm delete -n registry docker-registry
kubectl debug node/km-blue -n debug -it --image=busybox
rm -rf /host/var/mnt/cluster-data/docker-registry-pv/docker
cd /host/var/mnt/cluster-data/docker-registry-pv
cd /host/etc/cri/conf.d/hosts

## Tls
https://medium.com/mlearning-ai/deploy-a-private-container-registry-for-full-control-over-kubernetes-b32893695eaa

docker-registry.registry.svc.cluster.local

https://rpi4cluster.com/k3s/k3s-docker-tls/

openssl req -x509 -newkey rsa:4096 -sha256 -days 3650 -nodes -keyout tls.key -out tls.crt -subj "//CN=docker-registry.registry" -addext "subjectAltName=DNS:docker-registry.registry.svc.cluster.local,DNS:*.registry.svc.cluster.local,IP:*************"

C:\ProgramData\certify\assets\_.fernir.biz

openssl pkcs12 -in cert.pfx -nocerts -out tls.key

openssl pkcs12 -in cert.pfx -clcerts -nokeys -out tls.crt

sslshopper to convert pfx to key and crt
