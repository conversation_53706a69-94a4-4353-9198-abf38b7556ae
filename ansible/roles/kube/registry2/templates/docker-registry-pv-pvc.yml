---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: docker-registry-pv
spec:
  capacity:
    storage: {{volume_xxl}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: "{{cluster_data_dir}}/docker-registry-pv"
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists

---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: docker-registry-pvc
  namespace: registry
  labels:
    app: docker-registry
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: {{volume_xxl}}
  volumeName: docker-registry-pv
  storageClassName: local-storage
  volumeMode: Filesystem
