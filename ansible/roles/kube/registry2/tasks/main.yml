- name: Create namespace
  shell: kubectl create namespace registry --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Create local persistent volume
  shell: kubectl apply --context={{kubectl_context}} --namespace registry -f -
  args:
    stdin: "{{lookup('template', '{{registry_templates_dir}}/docker-registry-pv-pvc.yml')}}"

- name: Add twuni repo
  shell: helm repo add twuni https://helm.twun.io

- name: Deploy registry
  shell: helm install docker-registry --kube-context={{kubectl_context}} --namespace registry -f - twuni/docker-registry --version 2.2.3
  args:
    stdin: "{{lookup('file', '{{registry_templates_dir}}/docker-registry-overrides.yml')}}"

- name: Setup ingress
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{ lookup('template', '{{registry_templates_dir}}/docker-registry-ingress.yml')}}"
