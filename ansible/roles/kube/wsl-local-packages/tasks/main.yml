- name: <PERSON><PERSON> helm gpg and repository
  become: true
  shell: |
    curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | sudo tee /usr/share/keyrings/helm.gpg > /dev/null
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | sudo tee /etc/apt/sources.list.d/helm-stable-debian.list
  delegate_to: localhost

- name: Install helm
  become: true
  apt:
    name: "helm=3.17.1-1"
    state: present
    update_cache: true
  delegate_to: localhost

- name: Hold helm version
  become: true
  dpkg_selections:
    name: helm
    selection: hold
  delegate_to: localhost

- name: Add kubectl gpg and repository
  become: true
  shell: |
    curl -fsSL https://pkgs.k8s.io/core:/stable:/v{{kube_major_version_number}}/deb/Release.key | sudo gpg --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg
    mkdir -p /etc/apt/keyrings
    echo 'deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/v{{kube_major_version_number}}/deb/ /' | sudo tee /etc/apt/sources.list.d/kubernetes.list
  delegate_to: localhost

- name: Install kubectl
  become: true
  apt:
    name: "kubectl={{kube_major_version_number}}.2-1.1"
    state: present
    update_cache: true
  delegate_to: localhost

- name: Hold kubectl version
  become: true
  dpkg_selections:
    name: kubectl
    selection: hold
  delegate_to: localhost

- name: Install talosctl
  shell: curl -sL https://talos.dev/install | sh
  delegate_to: localhost


