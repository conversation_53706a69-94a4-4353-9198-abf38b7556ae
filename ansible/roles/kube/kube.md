# Get dashboard token

kubectl -n kubernetes-dashboard create token --duration 6232h dashboard-admin

# Switch cluster
kubectl config use-context kubernetes-admin@kubernetes-staging

kubectl config use-context kubernetes-admin@kubernetes

# Delete cluster

kubeadm reset --force

# Scale down things

kubectl scale deployment ks-web-prod -n=ks-prod --replicas=0

kubectl scale statefulsets postgres-postgresql -n=postgres --replicas=0

# If error "x509: certificate has expired or is not yet valid"

ssh into the box and run `kubeadm certs renew all` then reboot

# Drain node

kubectl scale deployment ks-web-prod -n=ks-prod --replicas=0

kubectl drain vpsgtmt0.s-host.host --ignore-daemonsets --delete-emptydir-data

kubectl uncordon vpsgtmt0.s-host.host

kubectl scale deployment ks-web-prod -n=ks-prod --replicas=1

# Access service through kube proxy

http://localhost:8001/api/v1/namespaces/<namespace_name>/services/<service_name>/proxy

service.namespace.svc.cluster.local

# Pods

## Debug node

kubectl create namespace debug
kubectl label ns debug pod-security.kubernetes.io/audit=privileged pod-security.kubernetes.io/enforce=privileged pod-security.kubernetes.io/warn=privileged
kubectl debug node/km-blue -n debug -it --image=busybox
cd /host/var/mnt/cluster-data

kubectl delete namespace debug

## Debug pod

kubectl run web --image=tutum/hello-world --port=80

kubectl debug postgres-postgresql-0 -n postgres -it --image=busybox

## Exec

kubectl exec -it postgres-postgresql-0 -n postgres -- sh

rm -rf /bitnami/postgresql/data

## Delete all evicted pods

for each in $(kubectl get pods|grep Evicted|awk '{print $1}'); do kubectl delete pods $each; done


## List kubelet events from journal

journalctl -xeu kubelet

