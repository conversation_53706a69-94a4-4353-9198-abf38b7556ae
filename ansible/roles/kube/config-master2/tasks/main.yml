- name: Create namespace {{item}}
  shell: kube<PERSON>l create namespace {{item}} --context={{kubectl_context}}
  loop: ["ks-prod", "postgres", "ks-staging", "registry", "ingress-nginx", "redmine", "saas-kit-lm", "saas-kit-staging"]

- name: Create postgresql-secret in namespace {{item}}
  shell: kubectl create secret generic postgresql-secret --context={{kubectl_context}} --namespace={{item}} --from-literal=postgres-password={{postgresql_password_dev}}
  loop: ["postgres", "ks-prod", "ks-staging", "saas-kit-staging", "saas-kit-lm"]
  ignore_errors: true

- name: Create priority classes
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/priority-classes.yml')}}"

- name: Create local storage class
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{lookup('file', '{{kub_config_master_templates_dir}}/persistent-volumes/local-storage-sc.yml')}}"

- name: Create local path storage class
  shell: kubectl apply -f -
  args:
    stdin: "{{lookup('file', '{{kub_config_master_templates_dir}}/persistent-volumes/local-path-sc.yml')}}"

- name: Create ks-web-prod pv and pvc
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/ks-web-prod-pv-pvc.yml')}}"

- name: Create ks-web-staging pv and pvc
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/persistent-volumes/ks-web-staging-pv-pvc.yml')}}"

- name: Create tls secret
  shell: kubectl create --context={{kubectl_context}} --namespace {{namespace}} -f -
  args:
    stdin: "{{lookup('template', '{{kub_config_master_templates_dir}}/tls-secret.yml')}}"
  loop: ["registry", "ingress-nginx", "postgres"]
  loop_control:
    loop_var: namespace
  when: cluster_environment != "prod"
