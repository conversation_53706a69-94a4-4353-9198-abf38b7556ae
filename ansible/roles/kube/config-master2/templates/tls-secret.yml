apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
type: kubernetes.io/tls
data:
  # values are base64 encoded, which obscures them but does NOT provide
  # any useful level of confidentiality
  tls.crt: |
    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
  tls.key: |
    ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
