---
kind: PersistentVolume
apiVersion: v1
metadata:
  name: ks-web-pv
  namespace: ks-ci
  labels:
    app: ks-web
spec:
  storageClassName: local-storage
  capacity:
    storage: {{volume_xs}}
  accessModes:
    - ReadWriteOnce
  local:
    path: "{{cluster_data_dir}}/ks-web-pv"
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: ks-web-pvc
    namespace: ks-ci

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ks-web-pvc
  namespace: ks-ci
spec:
  storageClassName: local-storage
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: {{volume_xs}}
