---
kind: PersistentVolume
apiVersion: v1
metadata:
  name: ks-web-staging-pv
  labels:
    app: ks-web-staging
spec:
  storageClassName: local-storage
  capacity:
    storage: {{volume_xxxl}}
  accessModes:
    - ReadWriteOnce
  local:
    path: "{{cluster_data_dir}}/ks-web-staging-pv"
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: ks-web-staging-pvc
    namespace: ks-staging

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app: ks-web-staging
  name: ks-web-staging-pvc
  namespace: ks-staging
spec:
  storageClassName: local-storage
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: {{volume_xxxl}}
