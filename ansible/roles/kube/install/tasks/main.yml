- name: Disable system swap
  become: yes
  shell: "swapoff -a"

- name: Remove current swaps from fstab
  become: yes
  lineinfile:
    dest: /etc/fstab
    regexp: '(?i)^([^#][\S]+\s+(none|swap)\s+swap.*)'
    line: '# \1'
    backrefs: yes
    state: present

- name: Ensure br_netfilter is enabled.
  become: yes
  modprobe:
    name: br_netfilter
    state: present

- name: Disable swappiness and pass bridged IPv4 traffic to iptable's chains
  become: yes
  sysctl:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    state: present
  with_items:
    - { name: 'vm.swappiness', value: '0' }
    - { name: 'net.bridge.bridge-nf-call-iptables', value: '1' }

- name: Add Kubernetes APT GPG key
  become: yes
  apt_key:
    url: https://packages.cloud.google.com/apt/doc/apt-key.gpg
    state: present

- name: Add Kubernetes APT repository
  become: yes
  apt_repository:
    repo: deb https://apt.kubernetes.io/ kubernetes-xenial main
    state: present
    filename: 'kubernetes'

- name: Install kubectl
  become: yes
  apt:
    name: "kubectl={{kube_version_number}}-00"
    state: present

- name: Hold kubectl version
  become: yes
  dpkg_selections:
    name: kubectl
    selection: hold

- name: Install kubelet
  become: yes
  apt:
    name: "kubelet={{kube_version_number}}-00"
    update_cache: yes
    state: present

- name: Hold kubelet version
  become: yes
  dpkg_selections:
    name: kubelet
    selection: hold

- name: Install kubeadm
  become: yes
  apt:
    name: "kubeadm={{kube_version_number}}-00"
    state: present

- name: Hold kubeadm version
  become: yes
  dpkg_selections:
    name: kubeadm
    selection: hold

- name: Install helm
  become: yes
  unarchive:
    src: https://get.helm.sh/helm-v3.10.0-linux-amd64.tar.gz
    dest: /usr/local/bin
    extra_opts:
      - --strip=1
      - --wildcards
      - '*/helm'
    remote_src: true
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"
    mode: "0750"

- name: Add stable repo to helm
  shell: helm repo add stable https://charts.helm.sh/stable

- name: Add bitnami repo to helm
  shell: helm repo add bitnami https://charts.bitnami.com/bitnami

- name: Copy kubeadm conf to drop-in directory
  become: yes
  template: src=20-extra-args.conf.j2 dest=/etc/systemd/system/kubelet.service.d/20-extra-args.conf

- name: Reload kubelet daemon
  become: yes
  systemd:
    name: kubelet
    daemon_reload: yes
    enabled: yes
