- name: Set timezone to UTC
  become: yes
  shell: timedatectl set-timezone UTC

- name: Set hostname
  become: yes
  shell: hostnamectl set-hostname {{node_hostname}}

- name: Set domain and hostname for ********* in /etc/hosts
  become: yes
  lineinfile:
    dest: /etc/hosts
    regexp: '^*********'
    line: '*********       {{node_hostname}}.{{node_domain}} {{node_hostname}}'

- name: Set domain and hostname
  become: yes
  lineinfile:
    dest: /etc/hosts
    regexp: '^{{ansible_host}}'
    line: '{{ansible_host}}       {{node_hostname}}.{{node_domain}} {{node_hostname}}'

- name: Update and upgrade apt packages
  become: true
  apt:
    upgrade: yes
    update_cache: yes
    cache_valid_time: 86400 #One day

