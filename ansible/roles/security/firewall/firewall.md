# Firewall

iptables -S

## Reapply iptables config
/sbin/iptables-restore -n /etc/iptables/iptables.conf

## Configure iptables

```bash
sudo iptables -S
```

[Docker and iptables](https://docs.docker.com/network/iptables/)

Block pings:

```bash
sudo iptables -A INPUT -p icmp --icmp-type echo-request -j REJECT
```

[Manage iptables firewall for Docker/Kubernetes](https://medium.com/swlh/manage-iptables-firewall-for-docker-kubernetes-daa5870aca4d)

[Docker meet firewall - finally an answer](https://unrouted.io/2017/08/15/docker-firewall/)

## See packet counters

```bash
iptables -L -v -n
```

## Ping allow

```bash
iptables -A INPUT -p icmp --icmp-type 8 -m state --state NEW,ESTABLISHED,RELATED -j ACCEPT
iptables -A OUTPUT -p icmp --icmp-type 0 -m state --state ESTABLISHED,RELATED -j ACCEPT
```

In global policy:

```yml
    - action: Allow
      protocol: UDP
      destination:
        ports: [137,138,139,445]
      source:
        nets:
          - {{vpn_network_admin_cidr}}
          - **************/32
          - *************/32
    - action: Allow
      protocol: ICMP
      icmp:
        type: 8
```
