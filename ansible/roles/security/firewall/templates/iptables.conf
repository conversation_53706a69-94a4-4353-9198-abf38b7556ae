*filter
:INPUT DROP [0:0]
:<PERSON>ORWARD ACCEPT [0:0]
:OUTPUT ACCEPT [0:0]
:INPUT-FILTERS - [0:0]
:FORWARD-FILTERS - [0:0]

-F INPUT-FILTERS
-F FORWARD-FILTERS

-A INPUT -j INPUT-FILTERS

-A FORWARD -j FORWARD-FILTERS

-A INPUT-FILTERS -m conntrack --ctstate INVALID -j DROP
-A INPUT-FILTERS -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
-A INPUT-FILTERS -i lo -j ACCEPT
-A INPUT-FILTERS -p tcp --dport 22 -m conntrack --ctstate NEW -j ACCEPT
-A INPUT-FILTERS -p tcp --dport 6016 -m conntrack --ctstate NEW -j ACCEPT
-A INPUT-FILTERS -p tcp --dport 6443 -s ************* -m conntrack --ctstate NEW -j ACCEPT
-A INPUT-FILTERS -p tcp --dport 80 -m conntrack --ctstate NEW -j ACCEPT
-A INPUT-FILTERS -i eth0 ! -s ************* -j REJECT --reject-with icmp-host-prohibited
-A INPUT-FILTERS -j RETURN

-A FORWARD-FILTERS -p tcp --dport 80 -m conntrack --ctstate NEW -j ACCEPT
-A FORWARD-FILTERS -i eth0 ! -s ************* -j REJECT --reject-with icmp-host-prohibited
-A FORWARD-FILTERS -j RETURN

COMMIT
