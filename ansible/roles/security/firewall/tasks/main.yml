- name: Create iptables directory
  become: yes
  file:
    path: "/etc/iptables"
    state: directory
    owner: root
    group: root

- name: Copy iptables.conf
  become: yes
  copy:
    src: "{{security_dir}}/firewall/templates/iptables.conf"
    dest: "/etc/iptables/iptables.conf"
    owner: root
    group: root
    mode: 0660

- name: Copy iptables service
  become: yes
  copy:
    src: "{{security_dir}}/firewall/templates/iptables.service"
    dest: "/etc/systemd/system/iptables.service"
    owner: root
    group: root
    mode: 0660

- name: Enable iptables service
  become: yes
  shell: systemctl enable --now iptables

- name: Disable ipv6
  become: yes
  lineinfile:
    dest: /etc/sysctl.conf
    regexp: "{{ item.regexp }}"
    line: "{{ item.line }}"
  loop:
    - { regexp: '^net.ipv6.conf.all.disable_ipv6', line: 'net.ipv6.conf.all.disable_ipv6=1' }
    - { regexp: '^net.ipv6.conf.default.disable_ipv6', line: 'net.ipv6.conf.default.disable_ipv6=1' }
    - { regexp: '^net.ipv6.conf.lo.disable_ipv6', line: 'net.ipv6.conf.lo.disable_ipv6=1' }

- name: Disable ipv6 apply
  become: yes
  shell: sysctl -p

