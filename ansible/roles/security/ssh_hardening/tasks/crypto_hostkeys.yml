---
- name: Replace default 2048 bits RSA keypair
  community.crypto.openssh_keypair:
    state: present
    type: rsa
    size: "{{ ssh_host_rsa_key_size }}"
    path: "{{ ssh_host_keys_dir }}/ssh_host_rsa_key"
    force: false
    regenerate: partial_idempotence

# In RHEL and Fedora, the 'ssh_keys' group is the group owner of the host private SSH keys.
# Since the openssh_keypair module needs to read the key to provide idempotency, we need to set ownership and group based on specific OS vars.
- name: Change host private key ownership, group and permissions
  file:
    path: "{{ ssh_host_keys_dir }}/ssh_host_rsa_key"
    owner: "{{ ssh_host_keys_owner }}"
    group: "{{ ssh_host_keys_group }}"
    mode: "0640"
  when: "ansible_facts.os_family == 'RedHat'"

- name: Set hostkeys according to openssh-version if openssh >= 5.3
  set_fact:
    ssh_host_key_files:
      - "{{ ssh_host_keys_dir }}/ssh_host_rsa_key"
  when: sshd_version is version('5.3', '>=')

- name: Set hostkeys according to openssh-version if openssh >= 6.0
  set_fact:
    ssh_host_key_files:
      - "{{ ssh_host_keys_dir }}/ssh_host_rsa_key"
      - "{{ ssh_host_keys_dir }}/ssh_host_ecdsa_key"
  when: sshd_version is version('6.0', '>=')

- name: Set hostkeys according to openssh-version if openssh >= 6.3
  set_fact:
    ssh_host_key_files:
      - "{{ ssh_host_keys_dir }}/ssh_host_rsa_key"
      - "{{ ssh_host_keys_dir }}/ssh_host_ecdsa_key"
      - "{{ ssh_host_keys_dir }}/ssh_host_ed25519_key"
  when: sshd_version is version('6.3', '>=')
