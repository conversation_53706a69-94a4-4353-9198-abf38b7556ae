---
- name: Set macs according to openssh-version if openssh >= 5.3
  set_fact:
    ssh_macs: '{{ ssh_macs_53_default }}'
  when: sshd_version is version('5.3', '>=')

- name: Set macs according to openssh-version if openssh >= 5.9
  set_fact:
    ssh_macs: '{{ ssh_macs_59_default }}'
  when: sshd_version is version('5.9', '>=')

- name: Set macs according to openssh-version if openssh >= 6.6
  set_fact:
    ssh_macs: '{{ ssh_macs_66_default }}'
  when: sshd_version is version('6.6', '>=')

- name: Set macs according to openssh-version if openssh >= 7.6
  set_fact:
    ssh_macs: '{{ ssh_macs_76_default }}'
  when: sshd_version is version('7.6', '>=')
