---
- name: Set ssh CA pub keys
  template:
    src: 'trusted_user_ca_keys.j2'
    dest: '{{ ssh_trusted_user_ca_keys_file }}'
    mode: '0644'
    owner: '{{ ssh_owner }}'
    group: '{{ ssh_group }}'
  notify: Restart sshd

- name: Create ssh authorized principals directories
  file:
    path: '{{ item.path | dirname }}'
    mode: '{{ item.directorymode | default("700") }}'
    owner: '{{ item.directoryowner | default(ssh_owner) }}'
    group: '{{ item.directorygroup | default(ssh_group) }}'
    state: directory
  loop: '{{ ssh_authorized_principals }}'

- name: Set ssh authorized principals
  template:
    src: 'authorized_principals.j2'
    dest: '{{ item.path }}'
    mode: '{{ item.filemode | default("600") }}'
    owner: '{{ item.owner| default(ssh_owner) }}'
    group: '{{ item.group | default(ssh_group) }}'
  loop: '{{ ssh_authorized_principals }}'
