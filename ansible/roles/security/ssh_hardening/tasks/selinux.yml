---
- name: Install selinux dependencies when selinux is installed
  package:
    name: '{{ ssh_selinux_packages }}'
    state: present

- name: Authorize {{ ssh_server_ports }} ports for selinux
  seport:
    ports: '{{ item }}'
    proto: tcp
    setype: ssh_port_t
    state: present
  loop: '{{ ssh_server_ports }}'

- name: Check if ssh_password module is already installed
  shell: 'set -o pipefail && semodule -l | grep ssh_password'
  args:
    executable: /bin/bash
  register: ssh_password_module
  failed_when: false
  changed_when: false
  check_mode: false

# The following tasks only get executed when selinux is in state enforcing,
# UsePam is 'no' and the ssh_password module is not installed. See this issue for
# more info: https://github.com/hardening-io/ansible-ssh-hardening/issues/23
- when:
    - not (ssh_use_pam | bool)
    - ('ssh_password' not in ssh_password_module.stdout)
  block:
    - name: Create selinux custom policy drop folder
      file:
        path: '{{ ssh_custom_selinux_dir }}'
        state: 'directory'
        owner: 'root'
        group: 'root'
        mode: '0750'

    - name: Distributing custom selinux policies
      copy:
        src: 'ssh_password'
        dest: '{{ ssh_custom_selinux_dir }}'
        owner: 'root'
        group: 'root'
        mode: '0600'

    - name: Check and compile policy
      command: checkmodule -M -m -o {{ ssh_custom_selinux_dir }}/ssh_password.mod {{ ssh_custom_selinux_dir }}/ssh_password

    - name: Create selinux policy module package
      command: semodule_package -o {{ ssh_custom_selinux_dir }}/ssh_password.pp  -m {{ ssh_custom_selinux_dir }}/ssh_password.mod

    - name: Install selinux policy
      command: semodule -i {{ ssh_custom_selinux_dir }}/ssh_password.pp


# The following tasks only get executed when selinux is installed, UsePam is
# 'yes' and the ssh_password module is installed. See
# http://danwalsh.livejournal.com/12333.html for more info
- name: Remove selinux-policy when Pam is used, because Allowing sshd to read the shadow file directly is considered a potential security risk
  command: semodule -r ssh_password
  when:
    - ssh_use_pam | bool
    - ('ssh_password' in ssh_password_module.stdout)
