{{ ansible_managed | comment }}
# Generated by Ansible role {{ ansible_role_name }}

#%PAM-1.0
auth        required      pam_env.so
auth        required      pam_faildelay.so delay=2000000
{% if os_auth_retries > 0 %}
auth        required      pam_faillock.so preauth silent audit even_deny_root deny={{ os_auth_retries }} unlock_time={{ os_auth_lockout_time }}
{% endif %}
{% if (os_auth_pam_sssd_enable | bool) %}
auth        [default=1 ignore=ignore success=ok] pam_succeed_if.so uid >= 1000 quiet
auth        [default=1 ignore=ignore success=ok] pam_localuser.so
{% endif %}
auth        sufficient    pam_unix.so nullok try_first_pass
{% if (os_auth_pam_sssd_enable | bool) %}
auth        [default=1 ignore=ignore success=ok] pam_succeed_if.so uid >= 1000 quiet
auth        sufficient    pam_sss.so forward_pass
{% endif %}
{% if os_auth_retries > 0 %}
auth        required      pam_faillock.so authfail audit even_deny_root deny={{ os_auth_retries }} unlock_time={{ os_auth_lockout_time }}
{% endif %}
auth        required      pam_deny.so

{% if os_auth_retries > 0 %}
account     required      pam_faillock.so
{% endif %}
account     required      pam_unix.so
account     sufficient    pam_localuser.so
account     sufficient    pam_succeed_if.so uid < 1000 quiet
{% if (os_auth_pam_sssd_enable | bool) %}
account     [default=bad success=ok user_unknown=ignore] pam_sss.so
{% endif %}
account     required      pam_permit.so

{% if (os_auth_pam_passwdqc_enable | bool) %}
password    requisite     pam_pwquality.so {{ os_auth_pam_pwquality_options }}
{% endif %}
{# NSA 2.3.3.6 Limit Password Reuse #}
password    requisite     pam_pwhistory.so remember={{ os_auth_pw_remember }} use_authtok
{# NSA 2.3.3.5 Upgrade Password Hashing Algorithm to SHA-512 #}
password    sufficient    pam_unix.so sha512 shadow nullok try_first_pass use_authtok rounds={{ os_sha_crypt_min_rounds }}
{% if (os_auth_pam_sssd_enable | bool) %}
password    sufficient    pam_sss.so use_authtok
{% endif %}
password    required      pam_deny.so

session     optional      pam_keyinit.so revoke
session     required      pam_limits.so
-session    optional      pam_systemd.so
session     [success=1 default=ignore] pam_succeed_if.so service in crond quiet use_uid
session     required      pam_unix.so
{% if (os_auth_pam_sssd_enable | bool) %}
session     optional      pam_sss.so
{% endif %}
