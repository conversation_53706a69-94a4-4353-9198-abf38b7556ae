{{ ansible_managed | comment }}
# Generated by Ansible role {{ ansible_role_name }}

# Configuration control definitions for the login package.
#
# Three items must be defined:  `MAIL_DIR`, `ENV_SUPATH`, and `ENV_PATH`. If unspecified, some arbitrary (and possibly incorrect) value will be assumed.  All other items are optional - if not specified then the described action or option will be inhibited.
#
# Comment lines (lines beginning with `#`) and blank lines are ignored.
#
#-- Modified for Linux.  --marekm

{% if os_useradd_mail_dir is defined %}
# *REQUIRED for useradd/userdel/usermod*
#
# Directory where mailboxes reside, _or_ name of file, relative to the home directory.  If you _do_ define `MAIL_DIR` and `MAIL_FILE`, `MAIL_DIR` takes precedence.
# Essentially:
#
# * `MAIL_DIR` defines the location of users mail spool files (for mbox use) by appending the username to `MAIL_DIR` as defined below.
# * `MAIL_FILE` defines the location of the users mail spool files as the fully-qualified filename obtained by prepending the user home directory before `$MAIL_FILE`
#
# *NOTE*: This is no more used for setting up users MAIL environment variable which is, starting from shadow 4.0.12-1 in Debian, entirely the job of the pam_mail PAM modules.
#
# See default PAM configuration files provided for login, su, etc.
# This is a temporary situation: setting these variables will soon move to `/etc/default/useradd` and the variables will then be no more supported
MAIL_DIR          {{ os_useradd_mail_dir }}
{% endif %}

{% if os_useradd_create_home is defined %}
# If useradd should create home directories for users by default
CREATE_HOME       {{ 'yes' if os_useradd_create_home else 'no' }}

{% endif %}
# Enable logging and display of `/var/log/faillog` login failure info. This option conflicts with the `pam_tally` PAM module.
FAILLOG_ENAB      yes

# Enable display of unknown usernames when login failures are recorded.
#
# *WARNING*: Unknown usernames may become world readable. See #290803 and #298773 for details about how this could become a security concern
LOG_UNKFAIL_ENAB  no

# Enable logging of successful logins
LOG_OK_LOGINS     yes

# Enable "syslog" logging of su activity - in addition to sulog file logging.
SYSLOG_SU_ENAB    yes

# Enable "syslog" logging of newgrp and sg.
SYSLOG_SG_ENAB    yes

# If defined, all su activity is logged to this file.
#SULOG_FILE        /var/log/sulog

# If defined, file which maps tty line to `TERM` environment parameter. Each line of the file is in a format something like "vt100  tty01".
#TTYTYPE_FILE      /etc/ttytype

# If defined, login failures will be logged here in a utmp format last, when invoked as lastb, will read `/var/log/btmp`, so...
FTMP_FILE         /var/log/btmp

# If defined, the command name to display when running "su -".  For # example, if this is defined as "su" then a "ps" will display the command is "-su".  If not defined, then "ps" would display the name of the shell actually being run, e.g. something like "-sh".
SU_NAME           su

# If defined, file which inhibits all the usual chatter during the login sequence.  If a full pathname, then hushed mode will be enabled if the user's name or shell are found in the file.  If not a full pathname, then hushed mode will be enabled if the file exists in the user's home directory.
#HUSHLOGIN_FILE    /etc/hushlogins
HUSHLOGIN_FILE    .hushlogin

# *REQUIRED*: The default PATH settings, for superuser and normal users. (they are minimal, add the rest in the shell startup files)
ENV_SUPATH        PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ENV_PATH          PATH=/usr/local/bin:/usr/bin:/bin:{{ os_env_extra_user_paths | join (':') }}

# Terminal permissions
# --------------------

# Login tty will be assigned this group ownership.
# If you have a "write" program which is "setgid" to a special group which owns the terminals, define `TTYGROUP` to the group number and `TTYPERM` to `0620`.  Otherwise leave `TTYGROUP` commented out and assign `TTYPERM` to either `622` or `600`.
TTYGROUP          tty

# Login tty will be set to this permission.
# In Debian `/usr/bin/bsd-write` or similar programs are setgid tty. However, the default and recommended value for `TTYPERM` is still `0600` to not allow anyone to write to anyone else console or terminal
# Users can still allow other people to write them by issuing the `mesg y` command.
TTYPERM           0600

# Login conf initializations
# --------------------------

# Terminal ERASE character ('\010' = backspace). Only used on System V.
ERASECHAR         0177

# Terminal KILL character ('\025' = CTRL/U). Only used on System V.
KILLCHAR          025

# The default umask value for `pam_umask` and is used by useradd and newusers to set the mode of the new home directories.
# If `USERGROUPS_ENAB` is set to `yes`, that will modify this `UMASK` default value for private user groups, i. e. the uid is the same as gid, and username is the same as the primary group name: for these, the user permissions will be used as group permissions, e. g. `022` will become `002`.
# Prefix these values with `0` to get octal, `0x` to get hexadecimal.
# `022` is the "historical" value in Debian for UMASK
# `027`, or even `077`, could be considered better for privacy.
UMASK             {{ os_env_umask }}

# Enable setting of the umask group bits to be the same as owner bits (examples: `022` -> `002`, `077` -> `007`) for non-root users, if the uid is the same as gid, and username is the same as the primary group name.
# If set to yes, userdel will remove the user´s group if it contains no more members, and useradd will create by default a group with the name of the user.
USERGROUPS_ENAB   yes


# Password aging controls
# -----------------------

# Maximum number of days a password may be used.
PASS_MAX_DAYS     {{ os_auth_pw_max_age }}

# Minimum number of days allowed between password changes.
PASS_MIN_DAYS     {{ os_auth_pw_min_age }}

# Number of days warning given before a password expires.
PASS_WARN_AGE     7

# Min/max values for automatic uid selection in useradd
UID_MIN           {{ os_auth_uid_min }}
UID_MAX           {{ os_auth_uid_max }}
# System accounts
SYS_UID_MIN       {{ os_auth_sys_uid_min }}
SYS_UID_MAX       {{ os_auth_sys_uid_max }}

# Min/max values for automatic gid selection in groupadd
GID_MIN           {{ os_auth_gid_min }}
GID_MAX           {{ os_auth_gid_max }}
# System accounts
SYS_GID_MIN       {{ os_auth_sys_gid_min }}
SYS_GID_MAX       {{ os_auth_sys_gid_max }}

# If /etc/subuid exists, the commands useradd and newusers (unless the user already have subordinate user IDs)
# allocate SUB_UID_COUNT unused user IDs from the range SUB_UID_MIN to SUB_UID_MAX for each new user.
# The default values for SUB_UID_MIN, SUB_UID_MAX, SUB_UID_COUNT are respectively 100000, ********* and 65536.
SUB_UID_MIN       {{ os_auth_sub_uid_min }}
SUB_UID_MAX       {{ os_auth_sub_uid_max }}
SUB_UID_COUNT     {{ os_auth_sub_uid_count }}
SUB_GID_MIN       {{ os_auth_sub_gid_min }}
SUB_GID_MAX       {{ os_auth_sub_gid_max }}
SUB_GID_COUNT     {{ os_auth_sub_gid_count }}

# Max number of login retries if password is bad. This will most likely be overriden by PAM, since the default pam_unix module has it's own built in of 3 retries. However, this is a safe fallback in case you are using an authentication module that does not enforce PAM_MAXTRIES.
LOGIN_RETRIES     {{ os_auth_retries }}

# Max time in seconds for login
LOGIN_TIMEOUT     {{ os_auth_timeout }}

# Which fields may be changed by regular users using chfn - use any combination of letters "frwh" (full name, room number, work phone, home phone).  If not defined, no changes are allowed.
# For backward compatibility, "yes" = "rwh" and "no" = "frwh".
{% if os_chfn_restrict %}
CHFN_RESTRICT     {{ os_chfn_restrict }}
{% endif %}
# Should login be allowed if we can't cd to the home directory?
DEFAULT_HOME      {{ 'yes' if os_auth_allow_homeless else 'no' }}

# If defined, this command is run when removing a user.
# It should remove any at/cron/print jobs etc. owned by
# the user to be removed (passed as the first argument).
#USERDEL_CMD       /usr/sbin/userdel_local

# Instead of the real user shell, the program specified by this parameter will be launched, although its visible name (`argv[0]`) will be the shell's. The program may do whatever it wants (logging, additional authentification, banner, ...) before running the actual shell.
#FAKE_SHELL        /bin/fakeshell

# If defined, either full pathname of a file containing device names or a ":" delimited list of device names.  Root logins will be allowed only upon these devices.
# This variable is used by login and su.
#CONSOLE           /etc/consoles
#CONSOLE           console:tty01:tty02:tty03:tty04

# List of groups to add to the user's supplementary group set when logging in on the console (as determined by the `CONSOLE` setting).  Default is none.
# Use with caution - it is possible for users to gain permanent access to these groups, even when not logged in on the console. How to do it is left as an exercise for the reader...
# This variable is used by login and su.
#CONSOLE_GROUPS    floppy:audio:cdrom

# If set to `MD5`, MD5-based algorithm will be used for encrypting password
# If set to `SHA256`, SHA256-based algorithm will be used for encrypting password
# If set to `SHA512`, SHA512-based algorithm will be used for encrypting password
# If set to `DES`, DES-based algorithm will be used for encrypting password (default)
# Overrides the MD5_CRYPT_ENAB option
#
# Note: It is recommended to use a value consistent with
# the PAM modules configuration.
MD5_CRYPT_ENAB    no
ENCRYPT_METHOD    SHA512

# Only used if `ENCRYPT_METHOD` is set to `SHA256` or `SHA512`: Define the number of SHA rounds.
# With a lot of rounds brute forcing the password is more difficult. But note also that it more CPU resources will be needed to authenticate users.
# If not specified, the libc will choose the default number of rounds (5000). The values must be inside the 1000-999999999 range. If only one of the MIN or MAX values is set, then this value will be used.
# If MIN > MAX, the highest value will be used.
SHA_CRYPT_MIN_ROUNDS    {{ os_sha_crypt_min_rounds }}
SHA_CRYPT_MAX_ROUNDS    {{ os_sha_crypt_max_rounds }}


# Obsoleted by PAM
# ================
# These options are now handled by PAM. Please edit the appropriate file in `/etc/pam.d/` to enable the equivelants of them.
#MOTD_FILE
#DIALUPS_CHECK_ENAB
#LASTLOG_ENAB
#MAIL_CHECK_ENAB
#OBSCURE_CHECKS_ENAB
#PORTTIME_CHECKS_ENAB
#SU_WHEEL_ONLY
#CRACKLIB_DICTPATH
#PASS_CHANGE_TRIES
#PASS_ALWAYS_WARN
#ENVIRON_FILE
#NOLOGINS_FILE
#ISSUE_FILE
#PASS_MIN_LEN
#PASS_MAX_LEN
#ULIMIT
#ENV_HZ
#CHFN_AUTH
#CHSH_AUTH
#FAIL_DELAY

# Obsoleted
# =========
# These options are no more handled by shadow.
# Shadow utilities will display a warning if they still appear.
#CLOSE_SESSIONS
#LOGIN_STRING
#NO_PASSWORD_CONSOLE
#QMAIL_DIR

# If set to `yes`, new passwords will be encrypted using the MD5-based algorithm compatible with the one used by recent releases of FreeBSD. It supports passwords of unlimited length and longer salt strings.
# Set to `no` if you need to copy encrypted passwords to other systems which don't understand the new algorithm.  Default is `no`.
# This variable is deprecated. You should use ENCRYPT_METHOD.
#
#MD5_CRYPT_ENAB no
