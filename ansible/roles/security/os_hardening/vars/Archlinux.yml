---

os_nologin_shell_path: '/sbin/nologin'

os_shadow_perms:
  owner: root
  group: root
  mode: '0600'

os_passwd_perms:
  owner: root
  group: root
  mode: '0644'

os_env_umask: '027'

os_auth_uid_min: 1000
os_auth_uid_max: 60000
os_auth_gid_min: 1000
os_auth_gid_max: 60000
os_auth_sys_uid_min: 500
os_auth_sys_uid_max: 999
os_auth_sys_gid_min: 500
os_auth_sys_gid_max: 999
os_auth_sub_uid_min: 100000
os_auth_sub_uid_max: 600100000
os_auth_sub_uid_count: 65536
os_auth_sub_gid_min: 100000
os_auth_sub_gid_max: 600100000
os_auth_sub_gid_count: 65536

modprobe_package: 'kmod'
auditd_package: 'audit'

hidepid_option: '2'  # allowed values: 0, 1, 2

sysctl_custom_config:
  # Mitigation of vulnerability CVE-2021-33909
  kernel.unprivileged_userns_clone: 0
  # Mitigation of vulnerability CVE-2021-33910
  kernel.unprivileged_bpf_disabled: 1
