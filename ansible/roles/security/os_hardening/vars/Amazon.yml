---

os_packages_pam_ccreds: 'pam_ccreds'
os_nologin_shell_path: '/sbin/nologin'

# Different distros use different standards for /etc/shadow perms, e.g.
# RHEL derivatives use root:root 0000, whereas Debian-based use root:shadow 0640.
# You must provide key/value pairs for owner, group, and mode if overriding.
os_shadow_perms:
  owner: root
  group: root
  mode: '0000'

os_passwd_perms:
  owner: root
  group: root
  mode: '0644'

os_env_umask: '077'

os_auth_uid_min: 1000
os_auth_uid_max: 60000
os_auth_gid_min: 1000
os_auth_gid_max: 60000
os_auth_sys_uid_min: 201
os_auth_sys_uid_max: 999
os_auth_sys_gid_min: 201
os_auth_sys_gid_max: 999
os_auth_sub_uid_min: 100000
os_auth_sub_uid_max: *********
os_auth_sub_uid_count: 65536
os_auth_sub_gid_min: 100000
os_auth_sub_gid_max: *********
os_auth_sub_gid_count: 65536

os_auth_pam_sssd_enable: false

# defaults for useradd
os_useradd_mail_dir: /var/spool/mail
os_useradd_create_home: true

modprobe_package: 'module-init-tools'
auditd_package: 'audit'

# system accounts that do not get their login disabled and pasword changed
os_always_ignore_users: ['root', 'sync', 'shutdown', 'halt', 'ec2-user']

hidepid_option: '2'  # allowed values: 0, 1, 2
