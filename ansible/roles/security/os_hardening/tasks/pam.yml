---
- name: Gather package facts
  package_facts:
    manager: auto
  when:
    - ansible_facts.os_family != 'Suse'
    - ansible_facts.os_family != 'Archlinux'

# the reason for this is so a user cannot connect to a server,
# that isn't connected to an LDAP server anymore.
# normally caching credentials shouldn't be necessary for most machines.
# removing it provides some more security while not removing usability.
- name: Remove pam ccreds to disable password caching
  package:
    name: '{{ os_packages_pam_ccreds }}'
    state: 'absent'
  when:
    - ansible_facts.os_family != 'Archlinux'

- import_tasks: pam_debian.yml
  when:
    - ansible_facts.os_family == 'Debian'

- import_tasks: pam_rhel.yml
  when:
    - ansible_facts.os_family == 'RedHat'

- name: NSA 2.3.3.5 Upgrade Password Hashing Algorithm to SHA-512
  template:
    src: 'etc/libuser.conf.j2'
    dest: '/etc/libuser.conf'
    mode: '0640'
    owner: 'root'
    group: 'root'
  when:
    - ansible_facts.os_family != 'Suse'
    - ansible_facts.os_family != 'Archlinux'
    - "'libuser' in ansible_facts.packages"
