---
- name: Install modprobe to disable filesystems | os-10
  package:
    name: '{{ modprobe_package }}'
    state: 'present'

- name: Check if efi is installed
  stat:
    path: "/sys/firmware/efi"
  register: efi_installed

- name: Remove vfat from fs-list if efi is used
  set_fact:
    os_unused_filesystems: "{{ os_unused_filesystems | difference('vfat') }}"
  when:
    - efi_installed.stat.isdir is defined
    - efi_installed.stat.isdir

- name: Remove used filesystems from fs-list
  set_fact:
    os_unused_filesystems: "{{ os_unused_filesystems | difference(ansible_mounts | map(attribute='fstype') | list) }}"

- name: Disable unused filesystems | os-10
  template:
    src: 'etc/modprobe.d/modprobe.j2'
    dest: '/etc/modprobe.d/dev-sec.conf'
    owner: 'root'
    group: 'root'
    mode: '0644'
