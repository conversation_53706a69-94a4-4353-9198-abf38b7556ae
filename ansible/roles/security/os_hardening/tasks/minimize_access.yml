---
# If the find-task throws an error on /usr/bin/X11 like "File system loop detected"
# the other files inside /usr/bin (and all other directories) are
# still getting found and the permissions minimized in the next task.
# This is also the reason why there's ignore_errors: true on the task.
# also see: https://github.com/dev-sec/ansible-os-hardening/issues/219
- name: Find files with write-permissions for group
  shell: "find -L {{ item }} -perm /go+w -type f"  # noqa command-instead-of-shell
  with_flattened:
    - '/usr/local/sbin'
    - '/usr/local/bin'
    - '/usr/sbin'
    - '/usr/bin'
    - '/sbin'
    - '/bin'
    - "{{ os_env_extra_user_paths }}"  # noqa deprecated-bare-vars
  register: minimize_access_directories
  ignore_errors: true
  changed_when: false

- name: Minimize access on found files
  file:
    path: '{{ item.1 }}'
    mode: 'go-w'
    state: file
  with_subelements:
    - "{{ minimize_access_directories.results }}"
    - stdout_lines

- name: Find shadow files
  stat:
    path: "{{ item }}"
  loop:
    - '/etc/shadow'
    - '/etc/gshadow'
    - '/etc/shadow-'
    - '/etc/gshadow-'
  register: minimize_access_shadow_files

- name: Change shadow ownership to root and mode to 0600 | os-02
  file:
    dest: "{{ item.item }}"
    owner: '{{ os_shadow_perms.owner }}'
    group: '{{ os_shadow_perms.group }}'
    mode: '{{ os_shadow_perms.mode }}'
  when: item.stat.exists
  loop: "{{ minimize_access_shadow_files.results }}"

- name: Find passwd files
  stat:
    path: "{{ item }}"
  loop:
    - '/etc/passwd'
    - '/etc/group'
    - '/etc/passwd-'
    - '/etc/group-'
  register: minimize_access_passwd_files

- name: Change passwd ownership to root and mode to 0644 | os-03
  file:
    dest: "{{ item.item }}"
    owner: '{{ os_passwd_perms.owner }}'
    group: '{{ os_passwd_perms.group }}'
    mode: '{{ os_passwd_perms.mode }}'
  when: item.stat.exists
  loop: "{{ minimize_access_passwd_files.results }}"

- name: Change su-binary to only be accessible to user and group root
  file:
    dest: '/bin/su'
    owner: 'root'
    group: 'root'
    mode: '0750'
  when: '"change_user" not in os_security_users_allow'

- name: Set option hidepid for proc filesystem
  mount:
    path: /proc
    src: proc
    fstype: proc
    opts: '{{ proc_mnt_options }}'
    state: mounted
