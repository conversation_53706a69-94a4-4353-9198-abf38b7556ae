---
- name: Remove unused repositories
  file:
    name: '/etc/yum.repos.d/{{ item }}.repo'
    state: 'absent'
  loop:
    - 'CentOS-Debuginfo'
    - 'CentOS-Media'
    - 'CentOS-Vault'
  when: os_security_packages_clean | bool

- name: Get yum repository files
  find:
    paths: '/etc/yum.repos.d'
    patterns: '*.repo'
  register: yum_repos

# for the 'default([])' see here:
# https://github.com/dev-sec/ansible-os-hardening/issues/99 and
# https://stackoverflow.com/questions/37067827/ansible-deprecation-warning-for-undefined-variable-despite-when-clause
- name: Activate gpg-check for yum repository files
  replace:
    path: '{{ item }}'
    regexp: '^\s*gpgcheck.*'
    replace: 'gpgcheck=1'
    mode: '0644'
  with_items:
    - "{{ yum_repos.files | default([]) | map(attribute='path') | difference(os_yum_repo_file_whitelist | map('regex_replace', '^', '/etc/yum.repos.d/') | list) }}"

# failed_when is needed because by default replace module will fail if the file doesn't exists.
# status.rc is only defined if an error accrued and only error code (rc) 257 will be ignored.
# All other errors will still be raised.
- name: Activate gpg-check for config files
  replace:
    path: '{{ item }}'
    regexp: '^\s*gpgcheck\W.*'
    replace: 'gpgcheck=1'
    mode: '0644'
  register: status
  failed_when: status.rc is defined and status.rc != 257
  loop:
    - '/etc/yum.conf'
    - '/etc/dnf/dnf.conf'
    - '/etc/yum/pluginconf.d/rhnplugin.conf'

- name: Remove deprecated or insecure packages | package-01 - package-09
  yum:
    name: '{{ os_security_packages_list }}'
    state: 'absent'
  when: os_security_packages_clean | bool
