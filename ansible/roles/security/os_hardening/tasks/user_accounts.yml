---
- name: Get UID_MIN from login.defs
  shell: awk '/^\s*UID_MIN\s*([0-9]*).*?$/ {print $2}' /etc/login.defs
  args:
    removes: /etc/login.defs
  register: uid_min
  check_mode: false
  changed_when: false

- name: Calculate UID_MAX from UID_MIN by substracting 1
  set_fact:
    uid_max: '{{ uid_min.stdout | int - 1 }}'
  when: uid_min.stdout|int > 0

- name: Set UID_MAX on Debian-systems if no login.defs exist
  set_fact:
    uid_max: '999'
  when:
    - ansible_facts.os_family == 'Debian'
    - uid_max is not defined

- name: Set UID_MAX on other systems if no login.defs exist
  set_fact:
    uid_max: '499'
  when: uid_max is not defined

- name: Get all system accounts
  command: awk -F'':'' '{ if ( $3 <= {{ uid_max|quote }} ) print $1}' /etc/passwd
  args:
    removes: /etc/passwd
  changed_when: false
  check_mode: false
  register: sys_accs

- name: <PERSON><PERSON><PERSON> always ignored system accounts from list
  set_fact:
    sys_accs_cond: '{{ sys_accs.stdout_lines | difference(os_always_ignore_users) }}'
  check_mode: false

- name: Change system accounts not on the user provided ignore-list
  user:
    name: '{{ item }}'
    shell: '{{ os_nologin_shell_path }}'
    password: '*'
    createhome: false
  with_flattened:
    - '{{ sys_accs_cond | default([]) | difference(os_ignore_users) | list }}'

- name: get all home directories in /home, but skip ignored users
  find:
    paths: /home/
    recurse: false
    file_type: directory
    excludes: "{{ os_ignore_home_folder_users | join(',') }}"
  register: home_directories
  when: os_chmod_home_folders | bool

- name: set ownership of /home directories to 0700
  file:
    mode: 0700
    path: "{{ item.path }}"
    state: directory
  loop: "{{ home_directories.files }}"
  when: os_chmod_home_folders | bool
