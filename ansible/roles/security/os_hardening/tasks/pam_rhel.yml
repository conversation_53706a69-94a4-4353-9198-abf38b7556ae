---
- name: Install sssd-clients
  yum:
    name: sssd-client
    state: 'present'
  when:
    - os_auth_pam_sssd_enable | bool

- name: Configure passwdqc and faillock via central system-auth config
  template:
    src: 'etc/pam.d/rhel_auth.j2'
    dest: '/etc/pam.d/system-auth-local'
    mode: '0640'
    owner: 'root'
    group: 'root'

- name: Enable our config for system-auth
  file:
    src: /etc/pam.d/system-auth-local
    dest: /etc/pam.d/system-auth
    mode: '0640'
    owner: 'root'
    group: 'root'
    state: link
    force: true

- name: Configure passwdqc and faillock via central password-auth config
  template:
    src: 'etc/pam.d/rhel_auth.j2'
    dest: '/etc/pam.d/password-auth-local'
    mode: '0640'
    owner: 'root'
    group: 'root'

- name: Enable our config for password-auth
  file:
    src: /etc/pam.d/password-auth-local
    dest: /etc/pam.d/password-auth
    mode: '0640'
    owner: 'root'
    group: 'root'
    state: link
    force: true
