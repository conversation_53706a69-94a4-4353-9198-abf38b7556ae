---
- name: disable coredumps
  block:
  - name: Create limits.d-directory if it does not exist | sysctl-31a, sysctl-31b
    file:
      path: '/etc/security/limits.d'
      owner: 'root'
      group: 'root'
      mode: '0755'
      state: 'directory'

  - name: Create additional limits config file -> 10.hardcore.conf | sysctl-31a, sysctl-31b
    pam_limits:
      dest: '/etc/security/limits.d/10.hardcore.conf'
      domain: '*'
      limit_type: hard
      limit_item: core
      value: '0'
      comment: Prevent core dumps for all users. These are usually not needed and may contain sensitive information

  - name: Set 10.hardcore.conf perms to 0400 and root ownership
    file:
      path: /etc/security/limits.d/10.hardcore.conf
      owner: 'root'
      group: 'root'
      mode: '0440'
      state: touch
      modification_time: preserve
      access_time: preserve

  - name: create coredump.conf.d-directory if it does not exist
    file:
      path: '/etc/systemd/coredump.conf.d'
      owner: root
      group: root
      mode: 0755
      state: directory
    when: ansible_service_mgr == "systemd"

  - name: create custom.conf for disabling coredumps
    template:
      src: 'etc/systemd/coredump.conf.d/coredumps.conf.j2'
      dest: '/etc/systemd/coredump.conf.d/custom.conf'
      owner: root
      group: root
      mode: 0644
    when: ansible_service_mgr == "systemd"
    notify: Reload systemd

  when: not os_security_kernel_enable_core_dump | bool

- name: enable coredumps
  block:
  - name: Remove coredump.conf.d directory with files
    file:
      path: /etc/systemd/coredump.conf.d
      state: absent
    when: ansible_service_mgr == "systemd"
    notify: Reload systemd

  - name: Remove 10.hardcore.conf config file
    file:
      path: /etc/security/limits.d/10.hardcore.conf
      state: absent
  when: os_security_kernel_enable_core_dump | bool
