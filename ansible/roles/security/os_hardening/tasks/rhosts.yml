---
- name: Get user accounts | os-09
  command: "awk -F: '{print $1}' /etc/passwd"
  changed_when: false
  check_mode: false
  register: users_accounts

- name: Delete rhosts-files from system | os-09
  file:
    dest: '~{{ item }}/.rhosts'
    state: 'absent'
  with_flattened: '{{ users_accounts.stdout_lines | default([]) }}'

- name: Delete hosts.equiv from system | os-01
  file:
    dest: '/etc/hosts.equiv'
    state: 'absent'

- name: Delete .netrc-files from system | os-09
  file:
    dest: '~{{ item }}/.netrc'
    state: 'absent'
  with_flattened: '{{ users_accounts.stdout_lines | default([]) }}'
