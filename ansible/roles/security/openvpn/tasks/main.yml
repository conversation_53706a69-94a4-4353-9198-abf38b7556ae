# script has customizations in it
- name: Copy openvpn-install-220509.sh to root directory
  become: true
  template:
    src: "{{openvpn_templates_dir}}/openvpn-install-220509.sh"
    dest: "/root"
    mode: 700

# action for backup, read "How to backup openvpn" section in openvpn.md
# - name: Fetch openvpn backup
#   become: true
#   ansible.builtin.fetch:
#     src: /root/server.tar.gz
#     dest: "{{openvpn_templates_dir}}/secrets"
