## Manual steps on prod
ssh to server, become root, run:

nano /etc/openvpn/auth/auth-ldap.conf

There replace admin and password with `cn=vpnauth,ou=Services,dc=fernir,dc=co` service creds. Then run:

<NAME_EMAIL>

## Manual steps on staging or local
Make sure that in ldap your users are inside ou=People,dc={{domain_name}},dc={{top_level_domain}}

# Hardening

[How to Harden OpenVPN](https://blog.securityevaluators.com/hardening-openvpn-in-2020-1672c3c4135a)

[Hardening OpenVPN Security](https://openvpn.net/community-resources/hardening-openvpn-security/)

```bash
sudo <NAME_EMAIL>

rm -rf /etc/openvpn/server/easy-rsa/pki
cp -r /etc/openvpn/server/easy-rsa/pki /root/pki
cp -r /root/pki /etc/openvpn/server/easy-rsa/pki
rm ca.crt ca.key crl.pem server.crt server.key tc.key
```

## How to backup openvpn

as root in home /root folder run:

```bash
tar -czvf server.tar.gz -C /etc/openvpn server
```

Run action in openvpn role "Fetch openvpn backup" to fetch the archive.

Copy the archive archive from templates/secrets/{host} to  to it-admin/vpn/backups

Later to restore run:

```bash
tar -xzvf server.tar.gz -C /etc/openvpn
```

## Configure OpenVPN Authentication via LDAP

[OpenVPN с авторизацией в OpenLDAP](https://www.lissyara.su/articles/freebsd/programms/openvpn+ldap/)

[Configure OpenVPN Authentication via LDAP (Active Directory)](https://poweradm.com/openvpn-ldap-ad-authentication/)

[Configuring the OpenVPN Auth-LDAP Plugin](https://github.com/threerings/openvpn-auth-ldap/wiki/Configuration)

### If you openldap certificate is untrusted and you want to configure application with ldap

For example, in the cluster_environment "staging" or "local" or you used Self-Signed Certificate.

Log in as root or superuser on Ubuntu.
Create directory for certificate if not exists:

```bash
mkdir /usr/local/share/ca-certificates
```

Get public certificate file from {{openldap_tls_secret_name}} secret:

```bash
kubectl get secret {{openldap_tls_secret_name}} -n openldap -o jsonpath="{.data.ca\.crt}" | base64 -d > /usr/local/share/ca-certificates/ca.crt
```

Update ca-certificates file:

```bash
update-ca-certificates
```

Restart openvpn:

```bash
<NAME_EMAIL>
```

[Installing a root CA certificate in the trust store](https://ubuntu.com/server/docs/security-trust-store)

[Get ca.crt from secret](https://gist.github.com/mreferre/6aae10ddc313dd28b72bdc9961949978)

### Restart openvpn server
<NAME_EMAIL>

### Edit config
nano /etc/openvpn/server/server.conf


### Logs
journalctl --utc --since "2023-06-02 00:00" -u openvpn
