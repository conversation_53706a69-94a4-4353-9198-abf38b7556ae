- name: Add a id_rsa.pub to authorized_keys (vagrant)
  lineinfile:
    path: /home/<USER>/.ssh/authorized_keys
    line: "{{ lookup('file', '{{ vagrant_pub_key }}')}}"
    create: yes

- name: Add ssh key to authorized_keys (Tolik laptop)
  lineinfile:
    path: /home/<USER>/.ssh/authorized_keys
    line: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC5TbvbvzE2+j1I/QmruJIG5cK0w7H6giuW4/lu5vXnMzWMHOXpAFIQ1fqx4I4a86yCEPEFflgnJ9Amq5s89xa9YkemgjUZnnR33S7cADFBHm3LLtzqo5Bm4bN82xOAFPFM+oPSagB3mqiJKEGDOcRv4E4iTPAFux+NULWUvsK3hgXGF7hM6wYirdzlTJBiFWUAn4DZIYpQthg7glk78LfXYoqzJAK4AnuSIo/lUrRlcAraUBIxiMEKrHO8/QtPZx5SNlszRo/rxWH/1oNm9fkCmNq9MRZHbJpyHOBRxZ42RUOry1me7GsMabTwABJJM8BhLuiidpLpRuh258OvC/azQDB5roqtG/CFDh1n+ciZ/6Iqsa4UPSGNI1rNXV3q4B82l7to1XREqTIQN7MCrts+hk3/BPw2JLIXYFW4uj0xQp7EaQsAOj9f2ONBMpkFEH1Bs9MUXUGSIEdzNJRoA+uHXF73F20I3uUifg6ZKGFiD+T65mdpsXX2PiUp6P/gy/G5aFQZT/MpzOW4cyyo/Z9vCmYM9Hzxg3/Wrsel34hzvb//z3A/t4RG2WtKr6Z+1H2nQ+a+9EYB4hUWEsOIPRUnGcj5JOiNBRnMP5TEE3sULMfPWgFQE4EMdodlQsAcZWwtto2Kz6E32F6un0sDpV7p+RTzigQdI0pIXCwHmRSYRw== <EMAIL>
    create: yes

- name: Add ssh key to authorized_keys (Tolik dell)
  lineinfile:
    path: /home/<USER>/.ssh/authorized_keys
    line: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCYFv3z0bFZTsETaCfbX2AO4a07zJb2N8joADzpkwxKxIGBe4X4ICH4+WGnJJQUtQJhGZvrQsM1XcNg2wAlxg91l7SMn5iq9CQYpEsU9KbZqOr3+R8cbpiri/7bvcIvhiHDEDVUI9U0GcZGsy05FzjbOfgsAlPHXzYMVgWCI7JA+VO+SzCUZYDAUlRaJx5+f0zvaRGkLceW/MhtgTmA3slz4UqyoUm0sAa/QxFrlnFwIupMZZB2m6TxcIjfm+uH2Yk7+jSnJkxOy/rr+DxHqkEHf7bglUw/syr1Wyg83LP2TEX7hoZLQqLBYNk4a4+/e07sfrhST+vqJOsoWaXXd8jl1V5NE0zoAckGGFbLj8cCwVurBBzUKN4GfVpwxAzLvecLqrY4ATbvCzdjomTwQha221VqkHHZto6sVR/YMTIRuGTinD+aOw65+dinyL/o5rELSlxQsPvwqHnEpuqFjx1Cec8uzWVq61KH2Re8wKUAgj3BcMxrnCEfUlTSlHGu0IE= agr23@Dell
    create: yes

- name: Disable login with password
  become: yes
  lineinfile:
    path: /etc/ssh/sshd_config
    regexp: '^\#?PasswordAuthentication (yes|no)$'
    line: PasswordAuthentication no

- name: Disable root login
  become: yes
  lineinfile:
    path: /etc/ssh/sshd_config
    regexp: '^\#?PermitRootLogin (yes|no)$'
    line: PermitRootLogin no

- name: Service ssh restart
  become: yes
  service:
    name: ssh
    state: restarted
