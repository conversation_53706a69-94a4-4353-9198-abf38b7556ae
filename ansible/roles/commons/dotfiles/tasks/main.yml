---

- name: Copy dotfiles to home
  copy: src={{ item.src }} dest={{ item.dest }}
  with_items:
    - { src: '{{dotfiles_dir}}/.bash_profile', dest: '{{home_dir}}' }
    - { src: '{{dotfiles_dir}}/.bashrc', dest: '{{home_dir}}' }
    - { src: '{{dotfiles_dir}}/.bash_aliases', dest: '{{home_dir}}' }
    - { src: '{{dotfiles_dir}}/.inputrc', dest: '{{home_dir}}' }
    - { src: '{{dotfiles_dir}}/.kubectl_aliases', dest: '{{home_dir}}' }

- name: Copy dotfiles to root
  become: yes
  copy: src={{ item.src }} dest={{ item.dest }}
  with_items:
    - { src: '{{dotfiles_dir}}/.bash_profile', dest: '/root' }
    - { src: '{{dotfiles_dir}}/.bashrc', dest: '/root' }
    - { src: '{{dotfiles_dir}}/.bash_aliases', dest: '/root' }
    - { src: '{{dotfiles_dir}}/.inputrc', dest: '/root' }
    - { src: '{{dotfiles_dir}}/.kubectl_aliases', dest: '/root' }
