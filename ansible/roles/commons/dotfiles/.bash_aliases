#!/bin/bash

# pass aliases to sudo
alias sudo="sudo "

# Windows
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "win32" ]]; then
    alias sudo=""
    alias vim="gvim"
fi

#ls
alias ll="ls -alF"
alias la="ls -A"
alias l="ls -CF"

#cd
alias c="cd"
alias ..="cd .."
alias ...="cd ../.."
alias ....="cd ../../.."
alias .....="cd ../../../.."

# mv, rm, cp
alias mv='mv -v'
alias cp='cp -v'

#linux
alias s="sudo"
alias sr="sudo !!"
alias sus="sudo service"
alias ssu="sudo su"
alias al="alias"

#apps
alias v="vim"
alias co="code"
alias coc="code ."
alias codo="code ~/'Google Drive'/fd/dotfiles/"
alias psl="powershell"

#npm
alias npi="npm install"
alias nps="npm start"
alias npd="npm install --save-dev"
alias npr="npm run"
alias npis="npm install --save"
alias npid="npm install --save-dev"
alias npus="npm uninstall --save"
alias npud="npm uninstall --save-dev"

#angular
alias ngb="ng build"
alias ngw="ng build --watch"
alias ngs="ng serve"
alias ngl="ng lint"
alias nga="ng build --aot --watch"

#git
alias g="git"
alias ga="git add --all"
alias gb="git branch "
alias gc="git commit -m "
function gitCommitPush() {
    git commit -m "$1" && git push
}
alias gcp=gitCommitPush
alias gacp="git add --all && gitCommitPush"
alias gca="git commit -am "
alias gce="git checkout"
alias gcn="git commit -nm "
alias gcem="git checkout master"
alias gd="git diff"
alias gdh="git diff HEAD"
alias gdw="git diff --word-diff"
alias gl="git pull"
alias glo="git log -15 --pretty=format:'%h %ad | %s%d [%an]' --graph"
alias gp="git push"
alias gpl="git pull && git submodule update --recursive"
alias gpw="git add --all && git commit -nm 'wip' && git push"
alias gs="git status"
alias gst="git stash"
alias gstl="git stash list"
alias gstd="git stash drop"
alias gstp="git stash pop"
alias gsta="git stash apply"
alias gsts="git stash show -p"
alias gsu="git submodule update --remote"
alias grsh="git reset --soft HEAD^"
alias grcc="git reset && git checkout . && git clean -fd"

#vagrant
alias va="vagrant"
alias vap="vagrant provision"
alias vau="vagrant up"
alias vas="vagrant ssh"
alias vasu="vagrant suspend"
alias var="vagrant reload"
alias vah="vagrant halt"
alias vasn="vagrant snapshot"
alias vasnl="vagrant snapshot list"
alias vasns="vagrant snapshot save"
alias vasnd="vagrant snapshot delete"
alias vasnr="vagrant snapshot restore"

#k8s
alias ku="kubectl"
alias kua="kubeadm"
alias kug="kubectl get"
alias kugp="kubectl get pods"
alias kugs="kubectl get svc"
alias kugr="kubectl get rs"
alias kugd="kubectl get deploy"
alias kugn="kubectl get nodes"
alias kude="kubectl delete"
alias kuded="kubectl delete deploy"
alias kudep="kubectl delete pod"
alias kudepf="kubectl delete pods --field-selector status.phase=Failed"
alias kud="kubectl describe"
alias kuds="kubectl describe svc"
alias kudp="kubectl describe pod"
alias kup="kubectl proxy"
alias kum="minikube"
alias kume="minikube docker-env"
alias kumd="minikube dashboard"
alias kums="minikube start"
alias kumst="minikube stop"
alias kumse="minikube service"

alias kust="vagrant up && kubectl proxy"

# helm
alias helde="helm delete --purge"

# docker
alias d="sudo docker"
alias dco="docker-compose"
alias dcou="docker-compose up"
alias dcd="docker-compose down"
alias doi="sudo docker images"
alias dori="sudo docker images"
alias dop="sudo docker ps"
alias dma="docker-machine"
alias dmae="docker-machine env"
alias dmas="docker-machine start"
alias dmast="docker-machine stop"

# terraform
alias tf="terraform"
alias tfa="terraform apply"
alias tfp="terraform plan"

# vboxmanage
alias vbm="vboxmanage"

# other
alias depl="cd /d/dev/depks && git pull && ./deploy/deploy-companion.sh -p" 
