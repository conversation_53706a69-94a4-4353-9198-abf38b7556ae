global:
  postgresql:
    auth:
      existingSecret: postgresql-secret
primary:
  priorityClassName: priority100000
  livenessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 20
    failureThreshold: 5
  startupProbe:
    enabled: true
    initialDelaySeconds: 20
    timeoutSeconds: 20
    failureThreshold: 5
  readinessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 20
    failureThreshold: 5
  persistence:
    enabled: false
  extraVolumes:
    - name: pg-ram-disk
      emptyDir:
        medium: Memory
        sizeLimit: 10Gi
  extraVolumeMounts:
    - name: pg-ram-disk
      mountPath: /pg-ram/postgresql
  resources:
    requests:
      cpu: '300m'
      memory: '250Mi'
    limits:
      cpu: '300m'
      memory: '250Mi'
  extendedConfiguration: |
pgHbaConfiguration: |
  host   all  all  127.0.0.1/32            scram-sha-256
  host   all  all  ::1/128                 scram-sha-256
  host   all  all  10.0.0.0/8              scram-sha-256
