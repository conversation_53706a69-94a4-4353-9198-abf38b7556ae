- name: Create namespace
  shell: kubectl create namespace postgres-ram --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Update helm repo
  shell: helm repo update

- name: Add bitnami full repo
  shell: helm repo add bitnami-full-index https://raw.githubusercontent.com/bitnami/charts/archive-full-index/bitnami

- name: Deploy postgres
  shell: helm install postgres --namespace postgres-ram -f - bitnami-full-index/postgresql --version 11.2.5
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres-ram/templates/postgres-overrides.yml')}}"
