# Manual parts

## [Postgresql proxy](http://localhost:8001/api/v1/namespaces/postgres/services/postgres-postgresql:5432/proxy)

### Add new server in pgAdmin web-interface

1. In Sidenav right-click on Browser.
2. <PERSON><PERSON> Create/Server
3. General Tab:

- Name: Postgres

4. Connection Tab:

- Host name/address: *************
- Port: 31003
- Password: password
- Save Password: true

```bash
helm delete -n postgres postgres
kubectl delete -n postgres pvc postgres-pvc
kubectl delete pv postgres-pv
cd /mnt/hdd/cluster-data/
sudo rm -r postgres-pv -f
```

## Drop database force

```sql
UPDATE pg_database SET datallowconn = 'false' WHERE datname = 'Ks-staging';

SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'Ks-staging';

DROP DATABASE "Ks-staging";
```

## Exec psql on postgres-postgresql-0

```bash
kubectl exec --namespace=postgres -it postgres-postgresql-0 -- psql 'user=postgres password=password host=localhost port=5432'
```

## Psql

```bash
psql -U postgres -p 31003 -h *************

scp /c/dev/backups/db.backup km@*************:~/db.backup

cp db.backup /mnt/hdd/cluster-data/postgres-pv
```

exec into postgres pod, in psql:

```sql
create database "Ks-prod";
```

list databases:

```sql
\l

pg_restore -U posrgres --verbose --dbname=Ks-prod db.backup
```
