- name: Create postgres pv directory
  file:
    path: "{{cluster_data_dir}}/postgres-pv"
    state: directory

- name: Create archive directory
  file:
    path: "{{cluster_data_dir}}/postgres-pv/wal-archive"
    state: directory

- name: Create local persistent volume (postgres pv)
  shell: kubectl apply --namespace postgres -f -
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres/templates/postgres-pv-pvc.yml')}}"

- name: Update helm repo
  shell: helm repo update

- name: Add bitnami full repo
  shell: helm repo add bitnami-full-index https://raw.githubusercontent.com/bitnami/charts/archive-full-index/bitnami

# Deploy to get tls certificate
- name: Deploy dummy ingress for postgres
  shell: kubectl apply --namespace postgres -f -
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres/templates/postgres-ingress.yml')}}"
  when: cluster_environment == "prod"

# postgres version 14.2.0
- name: Deploy postgres
  shell: helm install postgres --namespace postgres -f - bitnami-full-index/postgresql --version 11.2.5
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres/templates/postgres-overrides.yml')}}"

- name: Sleep 140 sec to start container
  shell: sleep 140

- name: Delete postgres
  shell: helm delete -n postgres postgres

- name: Redeploy postgres to change config
  shell: helm install postgres --namespace postgres -f - bitnami-full-index/postgresql --version 11.2.5
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres/templates/postgres-afterinit-overrides.yml')}}"

