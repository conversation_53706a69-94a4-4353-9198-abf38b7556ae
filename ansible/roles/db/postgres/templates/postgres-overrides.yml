#todo fd sec 1 is secret usage secure in kube. See caution https://kubernetes.io/docs/concepts/configuration/secret/
image:
  tag: 14.2.0-debian-10-r58
auth:
  existingSecret: postgresql-secret
primary:
  priorityClassName: priority100000
  livenessProbe:
    timeoutSeconds: 20
  startupProbe:
    enabled: true
    timeoutSeconds: 20
    failureThreshold: 30
  readinessProbe:
    timeoutSeconds: 20
    failureThreshold: 30
  service:
    type: NodePort
    nodePorts:
      postgresql: 31003
  persistence:
    size: "{{volume_xxxl}}"
    existingClaim: postgres-pvc
    storageClass: local-storage
  resources:
    requests:
      cpu: '400m'
      memory: '250Mi'
    limits:
      cpu: '400m'
      memory: '250Mi'
  configuration: |
    listen_addresses = '*'
    port = 5432
    max_connections = 1000
    wal_level = replica
    archive_mode = off
    archive_timeout = 300
    fsync = on
    max_wal_size = 400MB
    max_wal_senders = 16
    wal_keep_size = 192
    hot_standby = on
    log_connections = false
    log_disconnections = false
    log_hostname = false
    client_min_messages = error
    shared_preload_libraries = 'pgaudit'
    include_dir = 'conf.d'
    pgaudit.log_catalog = off
    archive_command = 'cp %p /bitnami/postgresql/wal-archive/%f'
    log_line_prefix = '%m [%p] %d %a '
  pgHbaConfiguration: |
    host     replication     postgres   {{ansible_host}}/32             md5
    host     all             all        0.0.0.0/0                    md5
    host     all             all        ::/0                         md5
    local    all             all                                     trust
    host     all             all        127.0.0.1/32                 md5
    host     all             all        ::1/128                      md5
