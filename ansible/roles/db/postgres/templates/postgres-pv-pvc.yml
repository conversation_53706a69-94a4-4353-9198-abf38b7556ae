apiVersion: v1
kind: PersistentVolume
metadata:
  name: postgres-pv
  labels:
    app: postgres
spec:
  capacity:
    storage: {{volume_xxxl}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: {{cluster_data_dir}}/postgres-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: postgres-pvc
    namespace: postgres
---

kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: postgres-pvc
  namespace: postgres
  labels:
    app: postgresql
spec:
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{volume_xxxl}}
  storageClassName: local-storage
  selector:
    matchLabels:
      app: postgres
