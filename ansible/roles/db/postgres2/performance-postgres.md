## PgBadger install

On ubuntu run

```bash
sudo apt-get install -y pgbadger
```

## PgBadger analysis

Enable Log min duration statement first, let it sit for a while.

--prefix take from postgres config log_line_prefix

```bash
 kubectl logs -n postgres postgres-postgresql-0 | pgbadger -b "2022-09-14 15:31:00" --prefix '%m [%p] %d %a ' --exclude-query='^(COPY|COMMIT)' -
```

## Log min statement

```sql
alter database "Ks-prod" set log_min_duration_statement = 10;
select pg_reload_conf();

alter database "Ks-prod" set log_min_duration_statement = -1;
select pg_reload_conf();
```

## Reload config

```sql
select pg_reload_conf();
```

## Non index scans

```sql
select schemaname, relname, seq_scan, seq_tup_read, seq_tup_read / seq_scan as avg,
	idx_scan, idx_tup_fetch, idx_tup_fetch / NULLIF(idx_scan,0) as idx_avg
from pg_stat_user_tables
where seq_scan > 0
order by seq_tup_read desc
```

## Slow queries

The module must be loaded by adding pg_stat_statements to shared_preload_libraries in postgresql.conf, because it requires additional shared memory.

```sql
SELECT substring(query, 1, 50) AS short_query,
              round(total_time::numeric, 2) AS total_time,
              calls,
              round(mean_time::numeric, 2) AS mean,
              round((100 * total_time /
              sum(total_time::numeric) OVER ())::numeric, 2) AS percentage_cpu
FROM    pg_stat_statements
ORDER BY total_time DESC
LIMIT 20;
```

## Concurrent index builds

If concurrent index create command fails then that invalid index has to be deleted manually.
