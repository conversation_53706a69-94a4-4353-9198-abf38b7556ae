- name: Create namespace
  shell: kube<PERSON>l create namespace postgres --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Create local persistent volume (postgres pv)
  shell: kubectl apply --context={{kubectl_context}} --namespace postgres -f -
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres/templates/postgres-pv-pvc.yml')}}"

# postgres version 16.4.15
- name: Deploy postgres
  shell: helm install postgres --kube-context={{kubectl_context}} --namespace postgres -f - oci://registry-1.docker.io/bitnamicharts/postgresql --version 16.5.0
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/postgres2/templates/postgres-overrides.yml')}}"



