## Postgresql proxy

http://localhost:8001/api/v1/namespaces/postgres/services/postgres-postgresql:5432/proxy

### Add new server in pgAdmin web-interface

- Host name/address: ************
- Port: 31003
- Password: password
- Save Password: true

postgres-postgresql.postgres.svc.cluster.local

## Delete postgres

helm delete -n postgres postgres
kubectl delete -n postgres pvc postgres-pvc && kubectl delete pv postgres-pv

kubectl debug node/talos-ucr-oz8 -n debug -it --image=busybox
rm -rf /host/var/mnt/cluster-data/postgres-pv/data && rm -rf /host/var/mnt/cluster-data/postgres-pv/conf
cd /host/var/mnt/cluster-data/postgres-pv

## Exec into pod

kubectl exec -it postgres-postgresql-0 -n postgres -- sh

cat /opt/bitnami/postgresql/conf/pg_hba.conf
kubectl exec -it postgres-postgresql-0 -n postgres -- cat //opt/bitnami/postgresql/conf/postgresql.conf

kubectl debug node/talos-ucr-oz8 -n debug -it --image=busybox

## Chekc if postgres is on tls

SELECT datname,usename, ssl, client_addr
  FROM pg_stat_ssl
  JOIN pg_stat_activity
    ON pg_stat_ssl.pid = pg_stat_activity.pid;

## Drop database force

UPDATE pg_database SET datallowconn = 'false' WHERE datname = 'Ks-staging';

SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'Ks-staging';

DROP DATABASE "Ks-staging";

## Exec psql on postgres-postgresql-0

kubectl exec --namespace=postgres -it postgres-postgresql-0 -- psql 'user=postgres password=password host=localhost port=5432'

## Psql

psql -U postgres -p 31003 -h *************

scp /c/dev/backups/db.backup km@*************:~/db.backup

cp db.backup /mnt/hdd/cluster-data/postgres-pv

exec into postgres pod, in psql:

create database "Ks-prod";

list databases:
\l

pg_restore -U posrgres --verbose --dbname=Ks-prod db.backup
