global:
  postgresql:
    auth:
      existingSecret: postgresql-secret
{% if cluster_environment == "prod" %}
tls:
  enabled: true
  certificatesSecret: tls-secret
  certFilename: tls.crt
  certKeyFilename: tls.key
{% endif %}
primary:
  priorityClassName: priority100000
  livenessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 20
    failureThreshold: 5
  startupProbe:
    enabled: true
    initialDelaySeconds: 20
    timeoutSeconds: 20
    failureThreshold: 5
  readinessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 20
    failureThreshold: 5
  persistence:
    existingClaim: postgres-pvc
  resources:
    requests:
      cpu: '300m'
      memory: '250Mi'
    limits:
      cpu: '300m'
      memory: '250Mi'
  extendedConfiguration: |
{% if cluster_environment == "prod" %}
    ssl = 'on'
    ssl_cert_file = '/opt/bitnami/postgresql/certs/tls.crt'
    ssl_key_file = '/opt/bitnami/postgresql/certs/tls.key'
{% endif %}
# td fd sec 1 first line with trust is insecure, workaround for bug https://github.com/bitnami/charts/issues/8650
  pgHbaConfiguration: |
{% if cluster_environment == "prod" %}
    local  postgres  postgres  trust
    local  all  all  scram-sha-256
    hostssl  all  all  127.0.0.1/32  scram-sha-256
    hostssl  all  all  ::1/128  scram-sha-256
    hostssl  all  all  0.0.0.0/0  scram-sha-256
    hostssl  all  all  ::/0  scram-sha-256
{% else %}
    local  postgres  postgres  trust
    host  all  all  0.0.0.0/0  scram-sha-256
    host  all  all  ::/0  scram-sha-256
    local  all  all  scram-sha-256
    host  all  all  127.0.0.1/32  scram-sha-256
    host  all  all  ::1/128  scram-sha-256
{% endif %}
