#intended to be run on host, not on km
- name: Add postgres key
  become: yes
  apt_key:
    url: https://www.postgresql.org/media/keys/ACCC4CF8.asc
    state: present

- name: Set variable lsb_release
  command: lsb_release -cs
  register: lsb_release

- name: Add Postgres apt repository
  become: yes
  apt_repository:
    repo: 'deb http://apt.postgresql.org/pub/repos/apt {{lsb_release.stdout}}-pgdg main'
    state: present
    filename: /etc/apt/sources.list.d/pgdg.list
    update_cache: no

- name: Install postgresql-client-11
  become: yes
  apt:
    update_cache: yes
    name: postgresql-client-11=11.10-1.pgdg16.04+1

- name: Create {{safe_dir}} directory
  file:
    path: "{{safe_dir}}"
    state: directory

- name: create {{base_backup_dir_path}} directory
  file:
    path: "{{base_backup_dir_path}}"
    state: directory

- name: Copy pg-dump-linux.sh to ubuntu
  template:
    src: ../templates/pg-dump-linux.sh
    dest: "{{safe_dir}}"

- name: Copy pg-basebackup-linux.sh to ubuntu
  template:
    src: ../templates/pg-basebackup-linux.sh
    dest: "{{safe_dir}}"

- name: Copy restore-databases-linux.sh to ubuntu
  template:
    src: ../templates/restore-databases-linux.sh
    dest: "{{safe_dir}}"

- name: Insert backup cron jobs to ubuntu crontab
  become: yes
  lineinfile:
    create: yes
    path: /etc/crontab
    line: "{{item}}"
  with_items:
    - '0 4 * * * root sh {{safe_dir}}/pg-basebackup-linux.sh'
