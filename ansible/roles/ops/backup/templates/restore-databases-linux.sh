#!/bin/bash
set -e
set -o nounset
set +u

#todo bt use node_ip ansible var
host="km@************"

#first run on ubuntu if ubuntu does not have kubeconfig
#scp $host:~/.kube/config ~/.kube

kubectl scale -n postgres statefulset postgres-postgresql --replicas=0
sleep 30

backup_folder=$1
if [ -z "$backup_folder" ]; then
  echo "restoring the latest backup"
  backup_folder=$(stat -c "%W %n" {{base_backup_dir_path}}/* | sort -k1,1n -r | cut -d " " -f2- | grep -vP "beforerestore" | head -n 1 | xargs basename)
fi

recovery_conf="restore_command='cp {{pod_wal_archive_dir_path}}/%f %p'\nrecovery_target_timeline='latest'"
echo $recovery_conf > {{base_backup_dir_path}}/$backup_folder/recovery.conf

scp -rp $host:{{pg_data_dir_path}} {{base_backup_dir_path}}/beforerestore$backup_folder
ssh $host rm -rf {{pg_data_dir_path}}
scp -rp {{base_backup_dir_path}}/$backup_folder $host:{{pg_data_dir_path}}

kubectl scale -n postgres statefulset postgres-postgresql --replicas=1
