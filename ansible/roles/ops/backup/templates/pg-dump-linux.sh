#!/bin/bash
set -e
set -o nounset
set +u

HOST="************";
PG_PORT="31003"

BACKUP_DIR_PATH="$1"

cd /

if [ -z "$1" ]
then
  BACKUP_DIR_PATH="/mnt/hdd/safe/backups";
fi

echo $BACKUP_DIR_PATH

for DB_NAME in "Ks-prod" "sonarDB";
do
DB_BACKUP_DIR_PATH="$BACKUP_DIR_PATH/$DB_NAME";
mkdir -p $DB_BACKUP_DIR_PATH;

BACKUP_TIME=$(date +%Y%m%d%H%M%S);
DB_FILE_PATH="$DB_BACKUP_DIR_PATH/$DB_NAME-$BACKUP_TIME.backup";
echo $DB_FILE_PATH;

# def password
/usr/lib/postgresql/11/bin/pg_dump --dbname=postgresql://postgres:password@$HOST:$PG_PORT/$DB_NAME --file="$DB_FILE_PATH" --verbose --role="postgres" --format=c;
done
