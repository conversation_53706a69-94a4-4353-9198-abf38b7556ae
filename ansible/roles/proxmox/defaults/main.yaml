---

proxmox:
  host: ***************
  node: pve
  validate_certs: no

vm_config:
  # Specifies the VM name
  name: Ubuntu-jump-host0
  # Specify emulated CPU type
  cpu: host
  # Specify number of cores per socket
  cores: 2
  # Sets the number of CPU sockets. (1 - N)
  sockets: 1
  # Memory size in MB for instance
  memory: 2048
  # Specifies guest operating system
  ostype: l26  # Linux 2.6/3.x/4.x/5.x kernel
  # Specify the boot order
  boot: order=scsi0
  # A hash/dictionary of volume used as SCSI hard disk
  scsi0: "local-lvm:10,format=raw"
  # A hash/dictionary of network interfaces for the VM
  net0: "virtio,bridge=vmbr0,firewall=1"
  storage: local-lvm
  bridge: vmbr0
  iso_storage: local
  # Timeout for operations
  timeout: 300
