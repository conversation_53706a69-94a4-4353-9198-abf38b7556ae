---

# To run this role:
# ansible-playbook -i hosts.ini your-playbook.yml --tags create_vm
#
# Required environment variables:
# export ANSIBLE_PROXMOX_USER="your-proxmox-user"
# export ANSIBLE_PROXMOX_PASSWORD="your-proxmox-password"
#
# Make sure to define vm_config and proxmox variables in your playbook or group_vars

- name: Install packages from requirements.txt
  ansible.builtin.pip:
    requirements: "{{ role_path }}/files/requirements.txt"
    state: present
  tags:
    - create_vm

- name: Create new VM
  community.general.proxmox_kvm:
    api_user: "{{ lookup('env', 'ANSIBLE_PROXMOX_USER') }}"
    api_password: "{{ lookup('env', 'ANSIBLE_PROXMOX_PASSWORD') }}"
    api_host: "{{ proxmox.host }}"
    validate_certs: "{{ proxmox.validate_certs }}"
    name: "{{ vm_config.name }}"
    node: "{{ proxmox.node }}"
    memory: "{{ vm_config.memory }}"
    cpu: "{{ vm_config.cpu }}"
    cores: "{{ vm_config.cores }}"
    sockets: "{{ vm_config.sockets }}"
    ostype: "{{ vm_config.ostype }}"
    # boot: "{{ vm_config.boot }}"
    scsi:
      scsi0: "{{ vm_config.scsi0 }}"
    net:
      net0: "{{ vm_config.net0 }}"
    state: present
    timeout: "{{ vm_config.timeout }}"
  tags:
    - create_vm
