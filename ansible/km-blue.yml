- hosts: kmBlue
  gather_facts: no
  roles:
    # - kube/wsl-local-packages # kubectl install might be broken, please fix the bug when encounter
    # manual, see main.yml for info
    # - kube/talos
    # - kube/config-master2 # manual
    #- secret/create-secrets-prod2
    # - security/cloudflared # after this connect to WARP
    # - kube/ingress-nginx
    # - kube/registry2
    # - db/postgres2
    # - secret/create-postgres-secrets
    # - apps/ks-static-file-server
    # - apps/redmine
    #- db/postgres-ram


# wsl ANSIBLE_CONFIG=//mnt/c/dev/cluster/ansible/ansible.cfg ansible-playbook ansible/km-blue.yml
