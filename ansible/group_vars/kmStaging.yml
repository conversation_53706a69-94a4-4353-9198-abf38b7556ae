ansible_connection: ssh
ansible_user: "km"
km_name: "km-staging"
node_name: "km"
node_user: "km"
node_hostname: "km"
network_interface: "eth0"
dir_creation_supported: true

kubectl_context: "kubernetes-admin@kubernetes-staging"

kube_major_version_number: 1.25
kube_version_number: "{{kube_major_version_number}}.0"
kube_version: v{{kube_version_number}}

vpn_address: "gw.{{domain_name}}.{{top_level_domain}}"
vpn_base_ip: "10.7.0."
vpn_network_ip_address: "{{vpn_base_ip}}0"
vpn_network_cidr: "{{vpn_network_ip_address}}/24"
vpn_network_admin_cidr: "{{vpn_base_ip}}2/32"

cluster_data_dir: "{{hdd_dir}}/cluster-data"
fast_cluster_data_dir: "{{ssd2_dir}}/fast-cluster-data"

load_balancer_base_ip: "10.252."
load_balancer_network: "{{load_balancer_base_ip}}0.0"

