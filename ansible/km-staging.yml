- hosts: kmStaging
  gather_facts: no
  roles:
    # - commons/ssh-keys
    # - vms/configure-vm
    # - commons/dotfiles
    # - {role: security/ssh_hardening, become: true, network_ipv6_enable: false} # after this role remove known_hosts files
    # - security/firewall #reboot because k<PERSON><PERSON><PERSON> will try to keep using ipv6
    # - {role: security/os_hardening, become: true, sysctl_overwrite: {net.ipv4.ip_forward: 1}, ufw_enable_ipv6: false}
    # - commons/packages
    # - kube/containerd
    # - kube/install
    # - kube/init-master
    # - kube/cni
    # - kube/cni-policy-staging
    # - kube/config-master # manual
    # - ops/monitoring/metrics-server # manual
    # - kube/registry
    # - db/postgres
    # - security/openvpn # manual
    # - kube/metallb
    # - kube/ingress-nginx
    # - apps/ks-static-file-server
    # - kube/cert-manager
    # - ops/logging
    # - apps/redmine-secret-before
    # - apps/redmine # manual
    # - apps/openldap # manual
    # - apps/openldap-secret # manual
    # - apps/samba

# vagrant ssh -c "(cd /usr/src/cluster/ansible && sudo ansible-playbook km-staging.yml)"

# ./hack/copy-vagrant-key-km-staging.sh

# This will not work because security/ssh_hardening breaks scp
# ./hack/post-init-km-staging.sh

# If you see REMOTE HOST IDENTIFICATION HAS CHANGED! then run:
# vagrant ssh -c "sudo rm /root/.ssh/known_hosts"
# rm /c/Users/<USER>/.ssh/known_hosts



