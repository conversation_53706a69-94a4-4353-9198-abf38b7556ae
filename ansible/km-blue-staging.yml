- hosts: kmBlueStaging
  gather_facts: no
  roles:
    # - kube/wsl-local-packages # kubectl install might be broken, please fix the bug when encounter
    - kube/talos
    # - kube/config-master2
    # - kube/ingress-nginx
    # - kube/registry2
    # - db/postgres2
    # - apps/redmine

# for ANSIBLE_CONFIG specify path that is correct for your machine. The path is in WSL
# wsl ANSIBLE_CONFIG=//mnt/c/dev/cluster/ansible/ansible.cfg ansible-playbook ansible/km-blue-staging.yml

# optionally can run inside wsl command line
# ANSIBLE_CONFIG=./ansible/ansible.cfg ansible-playbook ansible/km-blue-staging.yml
