- hosts: kmVps
  gather_facts: no
  roles:
    # - commons/ssh-keys
    # - vms/configure-vm
    # - commons/dotfiles
    # - {role: security/ssh_hardening, become: true, network_ipv6_enable: false} # after this role remove known_hosts files
    # - security/firewall #reboot because k<PERSON><PERSON><PERSON> will try to keep using ipv6
    # - {role: security/os_hardening, become: true, sysctl_overwrite: {net.ipv4.ip_forward: 1}, ufw_enable_ipv6: false}
    # - commons/packages
    # - kube/containerd
    # - kube/install
    # - kube/init-master
    # - kube/cni
    # - kube/cni-policy
    # - kube/config-master # manual
    # - create-secrets-prod
    # - kube/registry
    # - db/postgres
    # - ops/monitoring/metrics-server # manual
    # - kube/metallb
    # - kube/ingress-nginx # manual
    # - kube/cert-manager
    # - ops/logging
    # - apps/redmine-secret-before
    # - apps/redmine # manual
    # - apps/openldap # manual
    # - apps/openldap-secret # manual
    # - security/openvpn #manual
    # - apps/ks-web-prod
    # - apps/ks-static-file-server
    # - apps/sonarqube #manual

# vagrant ssh -c "(cd /usr/src/cluster/ansible && sudo ansible-playbook km-vps.yml)"
# ./hack/post-init-km-vps.sh
# rm /c/Users/<USER>/.ssh/known_hosts && vagrant ssh -c "sudo rm /root/.ssh/known_hosts"
# ./hack/copy-vagrant-key-km-vps.sh
