#!/bin/bash

VBoxManage createvm --name km --ostype Ubuntu_64 --register
VBoxManage storagectl km --name SATA --add sata --controller LSILogicSAS --bootable on --portcount 1
VBoxManage storageattach km --storagectl SATA --port 0 --device 0 --type hdd --medium ~/vms/ubuntu-16.04.vdi
VBoxManage modifyvm km --nic1 nat --nictype1 82540EM --cableconnected1 on
VBoxManage modifyvm km --nic2 bridged --nictype2 82543GC --bridgeadapter2 eno1 --cableconnected2 on
VBoxManage modifyvm km --memory 5124 --cpus 2 --natpf1 "guestssh,tcp,,2222,,22"

# vbm controlvm km poweroff soft

#Username: osboxes
#Password: osboxes.org
#Root Account Password: osboxes.org
