- hosts: localhost
  gather_facts: yes
  roles:
    # - commons/packages
    # - commons/ssh-keys
    # - commons/dotfiles
    # - kube/containerd
    # - kube/install
    # - kube/init-master
    # - kube/cni
    # - kube/config-master
    # - kube/registry
    # - db/postgres
    # - ci-cd/zalenium
    # - ci-cd/jenkins
    # - ops/logging
    # - ops/monitoring
    # - ops/pluto
    # - apps/sonarqube
    # - kube/metallb
    # - kube/nginx-ingress
    # - security/os_hardening
    # - security/ufw-configure-ports
    # - security/openvpn

# vagrant ssh -c "(cd /usr/src/cluster/ansible && sudo ansible-playbook local.yml)"
# ./hack/post-init.sh
