ansible_connection: local
ansible_python_interpreter: /usr/bin/python3
network_interface: "enp0s3"
ansible_host: "*************"
top_level_domain: "lan"
domain_name: "local"
domain: "{{domain_name}}.{{top_level_domain}}"
node_domain: "cl.local.lan"

cluster_environment: "local"

vpn_address: "*************"
vpn_base_ip: "10.6.0."
vpn_network_ip_address: "{{vpn_base_ip}}}0"
vpn_network_cidr: "{{vpn_network_ip_address}}/24"
vpn_network_admin_cidr: "{{vpn_base_ip}}}2/32"
