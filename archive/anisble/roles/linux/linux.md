#### This part exec on ubuntu (ssd)
vboxmanage controlvm km poweroff --soft
vboxmanage modifymedium disk "/home/<USER>/vms/ubuntu-20.04-staging.vdi" --resize 81920
vboxmanage startvm km --type=headless

#### This part exec on km-staging (ssd)
sudo su
parted -s /dev/sda rm 5
parted -s /dev/sda rm 2
reboot
sudo su
apt-get install cloud-guest-utils
growpart /dev/sda 1
resize2fs /dev/sda1

#### This part exec on ubuntu (hdd)
vboxmanage controlvm km poweroff --soft
vboxmanage modifymedium disk "/mnt/hdd/cluster-data-staging.vdi" --resize 262144
vboxmanage startvm km --type=headless

#### This part exec on km (hdd)
sudo su
apt-get install cloud-guest-utils
growpart /dev/sdb 1
resize2fs /dev/sdb1
