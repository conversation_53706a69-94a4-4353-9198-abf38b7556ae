# # use only if fast-cluster-data(-staging).vdi exists and should be deleted from /mnt/ssd2 on host
# - name: Unmount /dev/sdc1
#   become: yes
#   shell: umount /dev/sdc1
#   ignore_errors: true

# - name: Wipe ssd2
#   become: yes
#   shell: wipefs -a /dev/sdc
#   ignore_errors: true

# - name: Remove all files from folder {{ssd2_dir}}
#   become: yes
#   file:
#     path: "{{ssd2_dir}}/*"
#     state: absent

# # use only if fast-cluster-data(-staging).vdi does not exist in /mnt/ssd2 on host
# - name: Install parted
#   become: yes
#   apt:
#     name: parted
#     state: latest

# - name: Set partitioning standard mbr on /dev/sdc
#   become: yes
#   shell: parted /dev/sdc mklabel msdos

# - name: Create partition sdc1 on /dev/sdc
#   become: yes
#   shell: parted -a opt /dev/sdc mkpart primary ext4 0% 100%

# - name: Create filesystem ext4 and set label {{ssd2}} to /dev/sdc1
#   become: yes
#   shell: mkfs.ext4 -L {{ssd2}} /dev/sdc1

- name: Mount /dev/sdc1 to {{ssd2_dir}}
  become: yes
  mount:
    path: "{{ssd2_dir}}"
    src: LABEL={{ssd2}}
    fstype: ext4
    passno: "2"
    state: mounted

- name: Change owner of {{ssd2_dir}} {{ansible_user}}
  become: yes
  file:
    path: "{{ssd2_dir}}"
    state: directory
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"
    mode: '0770'

- name: Disable NCQ on sdc
  become: yes
  shell: echo 1 > /sys/block/sdc/device/queue_depth

- name: Create vms directory
  file:
    path: "{{ssd2_dir}}/vms"
    state: directory
