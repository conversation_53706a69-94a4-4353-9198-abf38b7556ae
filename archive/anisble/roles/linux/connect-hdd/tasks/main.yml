# # use only if cluster-data(-staging).vdi exists and should be deleted from /mnt/hdd on host
# - name: Unmount /dev/sdb1
#   become: yes
#   shell: umount /dev/sdb1
#   ignore_errors: true

# - name: Wipe hdd
#   become: yes
#   shell: wipefs -a /dev/sdb
#   ignore_errors: true

# - name: Remove all files from folder {{hdd}}
#   become: yes
#   file:
#     path: "{{hdd_dir}}/*"
#     state: absent

# # use only if cluster-data(-staging).vdi does not exist in /mnt/hdd on host
# - name: Install parted
#   become: yes
#   apt:
#     name: parted
#     state: latest

# - name: Set partitioning standard mbr on /dev/sdb
#   become: yes
#   shell: parted /dev/sdb mklabel msdos

# - name: Create partition sdb1 on /dev/sdb
#   become: yes
#   shell: parted -a opt /dev/sdb mkpart primary ext4 0% 100%

# - name: Create filesystem ext4 and set label {{hdd}} to /dev/sdb1
#   become: yes
#   shell: mkfs.ext4 -L {{hdd}} /dev/sdb1

- name: Mount /dev/sdb1 to {{hdd_dir}}
  become: yes
  mount:
    path: "{{hdd_dir}}"
    src: LABEL={{hdd}}
    fstype: ext4
    passno: "2"
    state: mounted

- name: Change owner of {{hdd_dir}} {{ansible_user}}
  become: yes
  file:
    path: "{{hdd_dir}}"
    state: directory
    owner: "{{ansible_user}}"
    group: "{{ansible_user}}"
    mode: '0770'

- name: Disable NCQ on sdb
  become: yes
  shell: echo 1 > /sys/block/sdb/device/queue_depth
