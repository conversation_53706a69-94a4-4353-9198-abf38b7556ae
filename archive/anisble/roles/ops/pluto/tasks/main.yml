- name: Install pluto
  become: yes
  unarchive:
    src: https://github.com/FairwindsOps/pluto/releases/download/v4.0.7/pluto_4.0.7_linux_amd64.tar.gz
    dest: /usr/local/bin
    remote_src: true

- name: Define deprecated apis
  become: yes
  shell: pluto detect-helm -owide --kubeconfig {{ kubeadmin_config }}
  register: plutoResult

- debug: msg={{ plutoResult.stdout}}

# inside local vagrant run:
# pluto detect-files -d /usr/src/cluster/ansible

#or run in local vagrant:
#pluto detect-helm -owide --kubeconfig /usr/src/cluster/.kube/config

# could also use https://github.com/rikatz/kubepug
# kubectl deprecations --k8s-version=v1.22.0-rc.0

