{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/brancz/kubernetes-grafana.git", "subdir": "grafana"}}, "version": "8ea4e7bc04b1bf5e9bd99918ca28c6271b42be0e", "sum": "muenICtKXABk6MZZHCZD2wCbmtiE96GwWRMGa1Rg+wA="}, {"source": {"git": {"remote": "https://github.com/etcd-io/etcd.git", "subdir": "contrib/mixin"}}, "version": "562d645ac923388ff5b8d270b0536764d34b0e0f", "sum": "W/Azptf1PoqjyMwJON96UY69MFugDA4IAYiKURscryc="}, {"source": {"git": {"remote": "https://github.com/grafana/grafonnet-lib.git", "subdir": "grafonnet"}}, "version": "3082bfca110166cd69533fa3c0875fdb1b68c329", "sum": "4/sUV0Kk+o8I+wlYxL9R6EPhL/NiLfYHk+NXlU64RUk="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "grafana-builder"}}, "version": "00795013f5975f518a0a3de99253f9d5590271c8", "sum": "GRf2GvwEU4jhXV+JOonXSZ4wdDv8mnHBPCQ6TUVd+g8="}, {"source": {"git": {"remote": "https://github.com/ksonnet/ksonnet-lib.git", "subdir": ""}}, "version": "0d2f82676817bbf9e4acf6495b2090205f323b9f", "sum": "h28BXZ7+vczxYJ2sCt8JuR9+yznRtU/iA6DCpQUrtEg=", "name": "ksonnet"}, {"source": {"git": {"remote": "https://github.com/kubernetes-monitoring/kubernetes-mixin.git", "subdir": ""}}, "version": "d0d7d5324f4d5333ee47e1895e726fe44bcb7094", "sum": "wQw1hzPBgZPKcdoBBFmlnimOUrPSrfwejVpzyV47Hwg="}, {"source": {"git": {"remote": "https://github.com/kubernetes-monitoring/kubernetes-mixin.git", "subdir": "lib/promgrafonnet"}}, "version": "08292d9bfabbf8d3cd85cdb73e159c9fc131144a", "sum": "zv7hXGui6BfHzE9wPatHI/AGZa4A2WKo6pq7ZdqBsps="}, {"source": {"git": {"remote": "https://github.com/kubernetes/kube-state-metrics.git", "subdir": "jsonnet/kube-state-metrics"}}, "version": "b1889aa1561ee269f628e2b9659155e7714dbbf0", "sum": "S5qI+PJUdNeYOv76jH5nxwYS9N6U7CRxvyuB1wI4cTE="}, {"source": {"git": {"remote": "https://github.com/kubernetes/kube-state-metrics.git", "subdir": "jsonnet/kube-state-metrics-mixin"}}, "version": "b1889aa1561ee269f628e2b9659155e7714dbbf0", "sum": "Yf8mNAHrV1YWzrdV8Ry5dJ8YblepTGw3C0Zp10XIYLo="}, {"source": {"git": {"remote": "https://github.com/prometheus-operator/kube-prometheus.git", "subdir": "jsonnet/kube-prometheus"}}, "version": "d666e4baa03eb342e7f5f55f9c4c3ca2e9243c4e", "sum": "e/4y/phKZ+cCDPCW6WAwSaO9kJk1TyyDwcR+/f9qkrQ="}, {"source": {"git": {"remote": "https://github.com/prometheus-operator/prometheus-operator.git", "subdir": "jsonnet/mixin"}}, "version": "b7ca32169844f0b5143f3e5e318fc05fa025df18", "sum": "6reUygVmQrLEWQzTKcH8ceDbvM+2ztK3z2VBR2K2l+U=", "name": "prometheus-operator-mixin"}, {"source": {"git": {"remote": "https://github.com/prometheus-operator/prometheus-operator.git", "subdir": "jsonnet/prometheus-operator"}}, "version": "b7ca32169844f0b5143f3e5e318fc05fa025df18", "sum": "MRwyChXdKG3anL2OWpbUu3qWc97w9J6YsjUWjLFQyB0="}, {"source": {"git": {"remote": "https://github.com/prometheus/alertmanager.git", "subdir": "doc/alertmanager-mixin"}}, "version": "99f64e944b1043c790784cf5373c8fb349816fc4", "sum": "V8jcZQ1Qrlm7AQ6wjbuQQsacPb0NvrcZovKyplmzW5w=", "name": "alertmanager"}, {"source": {"git": {"remote": "https://github.com/prometheus/node_exporter.git", "subdir": "docs/node-mixin"}}, "version": "b597c1244d7bef49e6f3359c87a56dd7707f6719", "sum": "cZTNXQMUCLB5FGYpMn845dcqGdkcYt58qCqOFIV/BoQ="}, {"source": {"git": {"remote": "https://github.com/prometheus/prometheus.git", "subdir": "documentation/prometheus-mixin"}}, "version": "6eeded0fdf760e81af75d9c44ce539ab77da4505", "sum": "VK0c3sQ3ksiM6JQsAVfWmL5NbzGv9llMfXFNXfFdJ+A=", "name": "prometheus"}, {"source": {"git": {"remote": "https://github.com/thanos-io/thanos.git", "subdir": "mixin"}}, "version": "ba6c5c4726ff52807c7383c68f2159b1af7980bb", "sum": "XP3uq7xcfKHsnWsz1v992csZhhZR3jQma6hFOfSViTs=", "name": "thanos-mixin"}], "legacyImports": false}