{"dependencies": [{"name": "etcd-mixin", "source": {"git": {"remote": "https://github.com/coreos/etcd", "subdir": "Documentation/etcd-mixin"}}, "version": "5adad5e22417bdb1eb7ee86baa3e4a77dddf9cb8", "sum": "Ko3qhNfC2vN/houLh6C0Ryacjv70gl0DVPGU/PQ4OD0="}, {"name": "grafana", "source": {"git": {"remote": "https://github.com/brancz/kubernetes-grafana", "subdir": "grafana"}}, "version": "539a90dbf63c812ad0194d8078dd776868a11c81", "sum": "b8faWX1qqLGyN67sA36oRqYZ5HX+tHBRMPtrWRqIysE="}, {"name": "grafana-builder", "source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs", "subdir": "grafana-builder"}}, "version": "f8a06bd7de550bd73921526a040f0db465133059", "sum": "ELsYwK+kGdzX1mee2Yy+/b2mdO4Y503BOCDkFzwmGbE="}, {"name": "grafonnet", "source": {"git": {"remote": "https://github.com/grafana/grafonnet-lib", "subdir": "grafonnet"}}, "version": "f3ee1d810858cf556d25f045b53cb0f1fd10b94e", "sum": "14YBZUP/cl8qi9u86xiuUS4eXQrEAam+4GSg6i9n9Ys="}, {"name": "ksonnet", "source": {"git": {"remote": "https://github.com/ksonnet/ksonnet-lib", "subdir": ""}}, "version": "0d2f82676817bbf9e4acf6495b2090205f323b9f", "sum": "h28BXZ7+vczxYJ2sCt8JuR9+yznRtU/iA6DCpQUrtEg="}, {"name": "kube-prometheus", "source": {"git": {"remote": "https://github.com/coreos/kube-prometheus", "subdir": "jsonnet/kube-prometheus"}}, "version": "ce5fe790ee9f1772ad52d935b320d545c8f88722", "sum": "AerKgmCkb6FsMAjPHqExxYSr6C/uYYq5qV9pAZULaxY="}, {"name": "kubernetes-mixin", "source": {"git": {"remote": "https://github.com/kubernetes-monitoring/kubernetes-mixin", "subdir": ""}}, "version": "25f35247681d00d248e7373bd74eca6b0ef7fe90", "sum": "oCGqDxxpoqIfcApkVC6VN9V3519GD5TTYyxShSXgCOE="}, {"name": "node-mixin", "source": {"git": {"remote": "https://github.com/prometheus/node_exporter", "subdir": "docs/node-mixin"}}, "version": "cafb12dc59f5b853bdd1167547056428eaddd353", "sum": "7vEamDTP9AApeiF4Zu9ZyXzDIs3rYHzwf9k7g8X+wsg="}, {"name": "prometheus", "source": {"git": {"remote": "https://github.com/prometheus/prometheus", "subdir": "documentation/prometheus-mixin"}}, "version": "ae93bae88fed884f59adef604ea059deff81650b", "sum": "/cohvDTaIiLElG66tKeQsi4v1M9mlGDKjOBSWivL9TU="}, {"name": "prometheus-operator", "source": {"git": {"remote": "https://github.com/coreos/prometheus-operator", "subdir": "jsonnet/prometheus-operator"}}, "version": "8d44e0990230144177f97cf62ae4f43b1c4e3168", "sum": "5U7/8MD3pF9O0YDTtUhg4vctkUBRVFxZxWUyhtNiBM8="}, {"name": "promgrafonnet", "source": {"git": {"remote": "https://github.com/kubernetes-monitoring/kubernetes-mixin", "subdir": "lib/promgrafonnet"}}, "version": "25f35247681d00d248e7373bd74eca6b0ef7fe90", "sum": "VhgBM39yv0f4bKv8VfGg4FXkg573evGDRalip9ypKbc="}, {"name": "slo-libsonnet", "source": {"git": {"remote": "https://github.com/metalmatze/slo-libsonnet", "subdir": "slo-libsonnet"}}, "version": "437c402c5f3ad86c3c16db8471f1649284fef0ee", "sum": "2Zcyku1f558VrUpMaJnI78fahDksPLcS1idmxxwcQ7Q="}]}