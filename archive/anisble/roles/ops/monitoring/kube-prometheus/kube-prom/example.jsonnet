local k = import 'ksonnet/ksonnet.beta.4/k.libsonnet';
local kp =
  (import 'kube-prometheus/kube-prometheus.libsonnet') +
  // Uncomment the following imports to enable its patches
  // (import 'kube-prometheus/kube-prometheus-anti-affinity.libsonnet') +
  // (import 'kube-prometheus/kube-prometheus-managed-cluster.libsonnet') +
  (import 'kube-prometheus/kube-prometheus-node-ports.libsonnet') +
  // (import 'kube-prometheus/kube-prometheus-static-etcd.libsonnet') +
  // (import 'kube-prometheus/kube-prometheus-thanos-sidecar.libsonnet') +
  {
    _config+:: {
      namespace: 'monitoring',
      prometheus+:: {
        replicas: 1,
      },
      alertmanager+:: {
        replicas: 1,
      },
      grafana+:: {
        config: { // http://docs.grafana.org/installation/configuration/
          sections: {
            "auth.anonymous": {enabled: true},
          },
        },
        container: {
          requests: { cpu: '0', memory: '68Mi' },
          limits: { cpu: '0', memory: '68Mi' },
        },
      },
      kubeStateMetrics+:: {
        baseMemory: '72Mi',
      },
      resources+:: {
        'node-exporter': {
          requests: { cpu: '0', memory: '60Mi' },
          limits: { cpu: '0', memory: '60Mi' },
        }
      },
    },
    prometheus+:: {
      prometheus+:
      local container = k.apps.v1.statefulSet.mixin.spec.template.spec.containersType;
      local resourceRequirements = container.mixin.resourcesType;
      local resources = resourceRequirements.new() + resourceRequirements.withRequests({ memory: '900Mi' }) +
        resourceRequirements.withLimits({ memory: '900Mi' });
      {
        spec+:{
          resources:resources
        },
      },
    },
    prometheusOperator+:: {
      deployment+:
      local container = k.apps.v1.deployment.mixin.spec.template.spec.containersType;
      local resourceRequirements = container.mixin.resourcesType;
      local resources = resourceRequirements.new() + resourceRequirements.withRequests({ cpu: '0', memory: '50Mi' }) +
        resourceRequirements.withLimits({ cpu: '0', memory: '50Mi' });
      {
        spec+: {
          template+: {
            spec+: {
              containers: std.map(function(container) container { resources: resources }, super.containers),
            },
          },
        },
      },
    },
    alertmanager+:: {
      alertmanager+: {
        spec+:{
          resources: {
            requests: { cpu: '0', memory: '52Mi' },
            limits: { cpu: '0', memory: '52Mi' },
          },
        },
      },
    },
    prometheusAlerts+:: {
      groups: std.map(
        function(group)
          if group.name == 'kubernetes-resources' || group.name == 'kubernetes-system-controller-manager' ||
            group.name == 'kubernetes-system-scheduler' then
            group {
              rules: std.filter(function(rule)
                //we are usine one node for now, so these would always fire.
                //kubeadm does not expose controller manager and scheduler to the cluser,
                //not sure how exposing them would affect security
                rule.alert != "KubeCPUOvercommit" && rule.alert != "KubeMemOvercommit" &&
                  rule.alert != "KubeControllerManagerDown" && rule.alert != "KubeSchedulerDown",
                group.rules
              )
            }
          else
            group,
        super.groups
      ),
    },
  };

{ ['setup/0namespace-' + name]: kp.kubePrometheus[name] for name in std.objectFields(kp.kubePrometheus) } +
{
  ['setup/prometheus-operator-' + name]: kp.prometheusOperator[name]
  for name in std.filter((function(name) name != 'serviceMonitor'), std.objectFields(kp.prometheusOperator))
} +
// serviceMonitor is separated so that it can be created after the CRDs are ready
{ 'prometheus-operator-serviceMonitor': kp.prometheusOperator.serviceMonitor } +
{ ['node-exporter-' + name]: kp.nodeExporter[name] for name in std.objectFields(kp.nodeExporter) } +
{ ['kube-state-metrics-' + name]: kp.kubeStateMetrics[name] for name in std.objectFields(kp.kubeStateMetrics) } +
{ ['alertmanager-' + name]: kp.alertmanager[name] for name in std.objectFields(kp.alertmanager) } +
{ ['prometheus-' + name]: kp.prometheus[name] for name in std.objectFields(kp.prometheus) } +
{ ['prometheus-adapter-' + name]: kp.prometheusAdapter[name] for name in std.objectFields(kp.prometheusAdapter) } +
{ ['grafana-' + name]: kp.grafana[name] for name in std.objectFields(kp.grafana) }
