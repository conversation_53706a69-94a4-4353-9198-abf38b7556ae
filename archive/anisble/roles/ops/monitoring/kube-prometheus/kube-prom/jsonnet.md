# Node exporter

```json
{
  "node-exporter-daemonset"+:{
    spec+: {
        template+: {
          spec+:
          local add_by_idx(idx) = (
            if idx == 0 then
            {
              resources+: {
                requests+: {
                  cpu: "50m",
                  memory: "50Mi"
                },
              },
            }
            else {}
          );
          {
            local modifiedContainers = std.mapWithIndex(function(i, v) v + add_by_idx(i), self.containers),
            containers2: modifiedContainers,//std.trace("Object: %s" % [modifiedContainers], modifiedContainers)
            containers: [],
          },
        },
    },
  },
}
```
