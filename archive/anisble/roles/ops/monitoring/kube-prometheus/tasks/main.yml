- name: Create kube-prometheus setup files
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('file', '{{item}}')}}"
  with_fileglob:
    - "{{kube_prometheus_dir}}/kube-prom2/manifests/setup/*.yaml"

- name: Create kube-prometheus other files
  shell: kubectl apply -f -
  args:
    stdin: "{{ lookup('file', '{{item}}')}}"
  with_fileglob:
    - "{{kube_prometheus_dir}}/kube-prom2/manifests/*.yaml"

# #todo fd enable backup of prometheus and related workloads later
# - name: Set BackupConfiguration paused (prometheus)
#   shell: "kubectl patch backupconfiguration sts-prometheus-k8s --namespace monitoring --type=\"merge\" --patch='{\"spec\": {\"paused\": true}}'"
#   when: restore_session | bool
#   ignore_errors: true

# - name: Create RestoreSession (prometheus)
#   shell: kubectl apply --namespace monitoring -f -
#   args:
#     stdin: "{{ lookup('template', '{{monitoring_templates_dir}}/prometheus-rs.yml')}}"
#   when: restore_session | bool

# - name: Set BackupConfiguration paused (grafana)
#   shell: "kubectl patch backupconfiguration deploy-grafana --namespace monitoring --type=\"merge\" --patch='{\"spec\": {\"paused\": true}}'"
#   when: restore_session | bool
#   ignore_errors: true

# - name: Create RestoreSession (grafana)
#   shell: kubectl apply --namespace monitoring -f -
#   args:
#     stdin: "{{ lookup('template', '{{monitoring_templates_dir}}/grafana-rs.yml')}}"
#   when: restore_session | bool

# #todo fd backupsession always runs - never succeed
# - name: Set BackupConfiguration paused (alertmanager)
#   shell: "kubectl patch backupconfiguration sts-alertmanager-main --namespace monitoring --type=\"merge\" --patch='{\"spec\": {\"paused\": true}}'"
#   when: restore_session | bool
#   ignore_errors: true

# - name: Create RestoreSession (grafana)
#   shell: kubectl apply --namespace monitoring -f -
#   args:
#     stdin: "{{ lookup('template', '{{monitoring_templates_dir}}/grafana-rs.yml')}}"
#   when: restore_session | bool
