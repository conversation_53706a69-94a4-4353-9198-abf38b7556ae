### grafana
http://localhost:8001/api/v1/namespaces/monitoring/services/grafana:3000/proxy/?orgId=1

kubectl --namespace monitoring port-forward svc/grafana 3000

30902

### prometheus
http://localhost:8001/api/v1/namespaces/monitoring/services/prometheus-k8s:9090/proxy

kubectl --namespace monitoring port-forward svc/prometheus-k8s 9090

30900

### alertmanager
http://localhost:8001/api/v1/namespaces/monitoring/services/alertmanager-main:9093/proxy

kubectl --namespace monitoring port-forward svc/alertmanager-main 9093

30903

### kube-prometheus

https://github.com/coreos/prometheus-operator/tree/master/contrib/kube-prometheus

minikube delete && minikube start --kubernetes-version=v1.13.2 --memory=4096 --bootstrapper=kubeadm --extra-config=kubelet.authentication-token-webhook=true --extra-config=kubelet.authorization-mode=Webhook --extra-config=scheduler.address=0.0.0.0 --extra-config=controller-manager.address=0.0.0.0

go get github.com/google/go-jsonnet/jsonnet

go get github.com/brancz/gojsontoyaml

go get github.com/jsonnet-bundler/jsonnet-bundler/cmd/jb

#### update jb
go get -u github.com/jsonnet-bundler/jsonnet-bundler/cmd/jb

jb install github.com/coreos/prometheus-operator/jsonnet/prometheus-operator

jb install github.com/coreos/prometheus-operator/contrib/kube-prometheus/jsonnet/kube-prometheus

./build.sh

./deploy.sh

### 06.08.2021 instructions 0.8 version

cd /usr/src/cluster/ansible/roles/ops/monitoring/kube-prometheus/kube-prom2

cp /usr/src/cluster/ansible/roles/ops/monitoring/kube-prometheus/kube-prom2/example.jsonnet ~/prom/example.jsonnet

rm -rf /usr/src/cluster/ansible/roles/ops/monitoring/kube-prometheus/kube-prom2/manifests && cp -avr ~/prom/manifests /usr/src/cluster/ansible/roles/ops/monitoring/kube-prometheus/kube-prom2/manifests


kubectl delete -n monitoring -f manifests
kubectl delete -n monitoring -f manifests/setup
