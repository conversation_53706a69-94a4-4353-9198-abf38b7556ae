- name: Create elasticsearch pv directory
  file:
    path: "{{cluster_data_dir}}/elasticsearch-pv-0"
    state: directory

- name: Apply local storage class
  shell: kubectl apply --namespace logging -f -
  args:
    stdin: "{{ lookup('template', '{{logging_templates_dir}}/elasticsearch-pv-0.yml')}}"

- name: Add elasticsearch helm repo
  shell: helm repo add elastic https://helm.elastic.co

- name: Deploy elasticsearch
  shell: helm install --namespace logging elasticsearch elastic/elasticsearch --version 7.17.3 -f -
  args:
    stdin: "{{ lookup('template', '{{logging_templates_dir}}/elasticsearch-overrides.yml')}}"

- name: Deploy kibana
  shell: helm install --namespace logging kibana elastic/kibana --version 7.17.3 -f -
  args:
    stdin: "{{ lookup('file', '{{logging_templates_dir}}/kibana-overrides.yml')}}"

- name: Add fluentd helm repo
  shell: helm repo add kokuwa https://kokuwaio.github.io/helm-charts

- name: Deploy fluentd
  shell: helm install --namespace logging fluentd-elasticsearch kokuwa/fluentd-elasticsearch --version 13.3.0 -f -
  args:
    stdin: "{{ lookup('file', '{{logging_templates_dir}}/fluentd-elasticsearch-overrides.yml')}}"

- pause:
    seconds: 90

- name: Wait until elasticsearch-master pod status will be changed to "Running"
  shell: "kubectl get pod --selector app=elasticsearch-master --no-headers --output custom-columns=\":status.phase\" --namespace logging"
  register: elasticsearch_pod_res
  retries: 10
  delay: 20
  until: elasticsearch_pod_res.stdout_lines | first == 'Running'

- pause:
    seconds: 30

- name: First time set logstash-* replicas to 0 to make cluster status green (inside pod method)
  become: yes
  shell: kubectl exec -ti --namespace logging elasticsearch-master-0 -- sh -c "curl -H 'Content-Type:application/json' -X PUT 'localhost:9200/logstash-*/_settings' -d '{ \"index.number_of_replicas\":0 }'"

- name: Permanently set logstash-* replicas to 0 to make cluster status green (inside pod method)
  become: yes
  shell: kubectl exec -ti --namespace logging elasticsearch-master-0 -- sh -c "curl -H 'Content-Type:application/json' -X PUT 'localhost:9200/_template/zero_number_of_replicas' -d '{ \"index_patterns\":[\"log*\"],\"settings\":{ \"number_of_replicas\":0 } }'"
