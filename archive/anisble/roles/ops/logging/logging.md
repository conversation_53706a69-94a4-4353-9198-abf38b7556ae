# Logging

## Delete es from cluster

```bash
helm uninstall -n logging elasticsearch

helm uninstall -n logging kibana

helm uninstall -n logging fluentd-elasticsearch

kubectl delete -n logging pvc elasticsearch-master-elasticsearch-master-0

kubectl delete pv elasticsearch-pv-0
```

## Check cluster status

```bash
curl ************:30905/_cluster/health?pretty

curl localhost:9200/_cluster/health?pretty
```

## Check indices status

```bash
curl ************:30905/_cat/indices

curl localhost:9200/_cat/indices
```

## Get explanation of allocation of any unassigned shards

```bash
curl ************:30905/_cluster/allocation/explain

curl localhost:9200/_cluster/allocation/explain
```

## Set logstash-* replicas to 0 to make cluster status green on km

```bash
curl -H 'Content-Type: application/json' -X PUT '************:30905/logstash-*/_settings' -d '{ "index.number_of_replicas" : 0 }'

curl -H 'Content-Type: application/json' -X PUT 'localhost:9200/logstash-*/_settings' -d '{ "index.number_of_replicas" : 0 }'
```

## Permanently set logstash-* replicas to 0 to make cluster status green on km

```bash
curl -H 'Content-Type:application/json' -X PUT '************:30905/_template/zero_number_of_replicas' -d '{ "index_patterns":["log*"],"settings":{ "number_of_replicas":0 } }'

curl -H 'Content-Type:application/json' -X PUT 'localhost:9200/_template/zero_number_of_replicas' -d '{ "index_patterns":["log*"],"settings":{ "number_of_replicas":0 } }'
```

## Set elasticsearch Java Heap Size to 300 Mb

```bash
kubectl set env -f http://localhost:8001/apis/apps/v1/namespaces/default/statefulsets/elasticsearch-master ES_JAVA_OPTS="-Xmx300m -Xms300m"
```

## Delete all logstash-* inside pod

```bash
curl -XDELETE localhost:9200/logstash-*
```
