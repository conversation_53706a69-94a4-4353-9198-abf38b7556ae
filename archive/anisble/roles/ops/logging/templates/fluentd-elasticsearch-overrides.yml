elasticsearch:
  hosts:
    - "elasticsearch-master:9200"
  ilm:
    enabled: true
    policy_id: logstash-policy
    policy:
      phases:
        hot:
          min_age: 0ms
          actions:
            set_priority:
              priority: 100
        delete:
          min_age: 30d
          actions:
            delete: {}
  template:
    enabled: true
    name: logstash-template
    file: logstash-template.json
    content: |-
      {
        "index_patterns": [
            "logstash-*"
        ],
        "settings": {
            "index": {
                "number_of_replicas": "0"
            }
        }
      }
resources:
  requests:
    cpu: '0'
    memory: '0'
  limits:
    cpu: '0'
    memory: '0'
configMaps:
  useDefaults:
    outputConf: false
extraConfigMaps:
   output.conf: |-
      <filter **postgres**>
        @id filter_concat_postgres
        @type concat
        key message
        separator ""
        multiline_start_regexp /^\d{4}-\d{2}-\d{2}.*/
        timeout_label @NORMAL
      </filter>

      <match **>
        @type relabel
        @label @NORMAL
      </match>

      <label @NORMAL>
        <match **ks-web-prod** **postgres**>
          @id elasticsearch
          @type "#{ENV['OUTPUT_TYPE']}"
          @log_level "#{ENV['OUTPUT_LOG_LEVEL']}"
          include_tag_key "#{ENV['OUTPUT_INCLUDE_TAG_KEY']}"
          hosts "#{ENV['OUTPUT_HOSTS']}"
          path "#{ENV['OUTPUT_PATH']}"
          scheme "#{ENV['OUTPUT_SCHEME']}"
          ssl_verify "#{ENV['OUTPUT_SSL_VERIFY']}"
          ssl_version "#{ENV['OUTPUT_SSL_VERSION']}"
          type_name "#{ENV['OUTPUT_TYPE_NAME']}"
          logstash_format "#{ENV['LOGSTASH_FORMAT']}"
          logstash_dateformat "#{ENV['LOGSTASH_DATEFORMAT']}"
          logstash_prefix "#{ENV['LOGSTASH_PREFIX']}"
          logstash_prefix_separator "#{ENV['LOGSTASH_PREFIX_SEPARATOR']}"
          enable_ilm "#{ENV['ENABLE_ILM']}"
          ilm_policy_id "#{ENV['ILM_POLICY_ID']}"
          ilm_policy "#{ENV['ILM_POLICY']}"
          ilm_policies "#{ENV['ILM_POLICIES']}"
          ilm_policy_overwrite "#{ENV['ILM_POLICY_OVERWRITE']}"
          template_name "#{ENV['TEMPLATE_NAME']}"
          template_file "#{ENV['TEMPLATE_FILE']}"
          template_overwrite "#{ENV['TEMPLATE_OVERWRITE']}"
          log_es_400_reason "#{ENV['OUTPUT_LOG_400_REASON']}"
          reconnect_on_error "#{ENV['OUTPUT_RECONNECT_ON_ERROR']}"
          reload_on_failure "#{ENV['OUTPUT_RELOAD_ON_FAILURE']}"
          reload_connections "#{ENV['OUTPUT_RELOAD_CONNECTIONS']}"
          request_timeout "#{ENV['OUTPUT_REQUEST_TIMEOUT']}"
          <buffer>
            @type "#{ENV['OUTPUT_BUFFER_TYPE']}"
            path "#{ENV['OUTPUT_BUFFER_PATH']}"
            flush_mode "#{ENV['OUTPUT_BUFFER_FLUSH_MODE']}"
            retry_type "#{ENV['OUTPUT_BUFFER_RETRY_TYPE']}"
            flush_thread_count "#{ENV['OUTPUT_BUFFER_FLUSH_THREAD_TYPE']}"
            flush_interval "#{ENV['OUTPUT_BUFFER_FLUSH_INTERVAL']}"
            retry_forever "#{ENV['OUTPUT_BUFFER_RETRY_FOREVER']}"
            retry_max_interval "#{ENV['OUTPUT_BUFFER_RETRY_MAX_INTERVAL']}"
            chunk_limit_size "#{ENV['OUTPUT_BUFFER_CHUNK_LIMIT']}"
            total_limit_size "#{ENV['OUTPUT_BUFFER_TOTAL_LIMIT_SIZE']}"
            overflow_action "#{ENV['OUTPUT_BUFFER_OVERFLOW_ACTION']}"
          </buffer>
        </match>
      </label>
