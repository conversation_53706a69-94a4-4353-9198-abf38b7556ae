apiVersion: v1
kind: PersistentVolume
metadata:
  name: elasticsearch-pv-0
spec:
  capacity:
    storage: {{volume_xxxl}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: {{cluster_data_dir}}/elasticsearch-pv-0
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    namespace: logging
    name: elasticsearch-master-elasticsearch-master-0
