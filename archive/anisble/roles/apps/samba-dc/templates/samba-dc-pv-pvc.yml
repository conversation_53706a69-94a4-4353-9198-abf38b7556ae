apiVersion: v1
kind: PersistentVolume
metadata:
  name: samba-dc-pv
  labels:
    app: samba-dc
spec:
  capacity:
    storage: {{volume_xxxl}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: {{cluster_data_dir}}/samba-dc-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: samba-dc-pvc
    namespace: samba-dc

---

kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: samba-dc-pvc
  namespace: samba-dc
  labels:
    app: samba-dc
spec:
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{volume_xxxl}}
  storageClassName: local-storage
  selector:
    matchLabels:
      app: samba-dc
