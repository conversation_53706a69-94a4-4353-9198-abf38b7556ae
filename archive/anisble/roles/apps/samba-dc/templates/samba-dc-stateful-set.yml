---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app.kubernetes.io/name: samba-dc
    release: "0.1"
  name: samba-dc
  namespace: samba-dc
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: samba-dc
      release: "0.1"
  serviceName: samba-dc
  template:
    metadata:
      labels:
        app.kubernetes.io/name: samba-dc
        release: "0.1"
    spec:
      containers:
      - name: samba-dc
        env:
        - { name: DOMAIN_ACTION, value: provision }
        - { name: INTERFACES, value: lo eth0 tun0 tunl0 }
        - { name: BIND_INTERFACES_ONLY, value: "no" }
        - { name: NETBIOS_NAME, value: vpsgtmt0 }
        - { name: REALM, value: AD.FERNIR.CO }
        - { name: TZ, value: UTC }
        - { name: WORKGRO<PERSON>, value: FERNIR }
        image: instantlinux/samba-dc:4.15.7-r0
        securityContext:
          privileged: true
          allowPrivilegeEscalation: true
          capabilities:
            add: ["SYS_ADMIN"]
        resources:
          limits:
            cpu: 150m
            memory: 512Mi
          requests:
            cpu: 150m
            memory: 512Mi
        volumeMounts:
        - mountPath: /var/lib/samba
          name: var
        - mountPath: /run/secrets/samba-admin-password
          name: samba-admin-password
          subPath: samba-admin-password
      dnsConfig:
        nameservers: [{{node_ip}}]
      # TODO: this will join with incorrect hostname until the following
      #  directive is actually implemented when hostNetwork=true.
      #  See issue https://github.com/kubernetes/kubernetes/issues/67019
      hostname: vpsgtmt0.ad.fernir.co
      hostNetwork: true
      #nodeSelector:
      #  service.samba-dc: allow
      #serviceAccountName: example-privileged
      volumes:
      - name: samba-admin-password
        secret:
          secretName: samba-admin-password
      - name: var
        persistentVolumeClaim:
          claimName: samba-dc-pvc
