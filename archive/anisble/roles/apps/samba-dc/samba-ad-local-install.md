### Local install

https://wiki.samba.org/index.php/Setting_up_Samba_as_an_Active_Directory_Domain_Controller

https://adamtheautomator.com/samba-active-directory/

apt-get install acl attr samba samba-dsdb-modules samba-vfs-modules winbind libpam-winbind libnss-winbind krb5-config krb5-user dnsutils

ping -c3 local.samdom.example.com

systemctl disable --now systemd-resolved

unlink /etc/resolv.conf

touch /etc/resolv.conf

nano /etc/resolv.conf

search samdom.example.com
nameserver *************

chattr +i /etc/resolv.conf

unmask https://adamtheautomator.com/samba-active-directory/

systemctl disable --now smbd nmbd winbind

systemctl unmask samba-ad-dc

systemctl enable samba-ad-dc

samba-tool domain provision --server-role=dc --use-rfc2307 --dns-backend=SAMBA_INTERNAL --realm=SAMDOM.EXAMPLE.COM --domain=SAMDOM --adminpass=Passw0rd

cp /var/lib/samba/private/krb5.conf /etc/krb5.conf

to /etc/samba/smb.conf add line in global: dns forwarder = ******* *******

systemctl start samba-ad-dc

smbclient -L localhost -N

smbclient //localhost/netlogon -UAdministrator -c 'ls'

host -t SRV _ldap._tcp.samdom.example.com.

host -t SRV _kerberos._udp.samdom.example.com.

host -t A local.samdom.example.com

kinit administrator

klist

https://adamtheautomator.com/samba-active-directory/

samba-tool user create alice alice_password88

samba-tool user list

Get-NetAdapter -Name "*"

Set-DNSClientServerAddress "Ethernet 12" –ServerAddresses ("*************","*******")

Get-DnsClientServerAddress

ping local.samdom.example.com

ping samdom.example.com

Add-Computer -DomainName "samdom.example.com" -Restart
