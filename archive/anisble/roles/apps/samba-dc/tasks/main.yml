- name: Create pv directory
  become: true
  file:
    path: "{{cluster_data_dir}}/samba-dc-pv"
    state: directory

- name: Create namespace
  shell: kubectl create namespace samba-dc --dry-run=client -o yaml | kubectl apply -f -

- name: Create local persistent volume
  shell: kubectl apply --namespace samba-dc -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/samba-dc/templates/samba-dc-pv-pvc.yml')}}"

- name: Deploy
  shell: kubectl apply --namespace samba-dc -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/samba-dc/templates/samba-dc-stateful-set.yml')}}"
