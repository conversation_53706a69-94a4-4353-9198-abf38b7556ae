### Cleanup if samba was installed before
rm -rf /var/lib/samba
rm -rf /etc/samba
rm /etc/krb5.conf

### Prod Install
https://wiki.samba.org/index.php/Setting_up_Samba_as_an_Active_Directory_Domain_Controller

https://adamtheautomator.com/samba-active-directory/

apt-get install acl attr samba samba-dsdb-modules samba-vfs-modules winbind libpam-winbind libnss-winbind krb5-config krb5-user dnsutils

#### For Kerberos specify:
Realm: AD.FERNIR.CO
Host: vpsgtmt0
Administrative server: vpsgtmt0

Verify that the /etc/hosts has line:
************* vpsgtmt0.ad.fernir.co vpsgtmt0

ping -c3 vpsgtmt0.ad.fernir.co

systemctl disable --now systemd-resolved

how to undo previous command ( ln -s /run/systemd/resolve/stub-resolv.conf /etc/resolv.conf  ,
systemctl enable systemd-resolved && sudo systemctl start systemd-resolved )

unlink /etc/resolv.conf

touch /etc/resolv.conf

nano /etc/resolv.conf

search ad.fernir.co
nameserver *************

chattr -i /etc/resolv.conf

systemctl disable --now smbd nmbd winbind

systemctl unmask samba-ad-dc

systemctl enable samba-ad-dc

!!! set password below
samba-tool domain provision --server-role=dc --use-rfc2307 --dns-backend=SAMBA_INTERNAL --realm=AD.FERNIR.CO --domain=FERNIR --adminpass=<< set password >> --option="interfaces=lo eth0 tun0" --option="bind interfaces only=yes"

cp /var/lib/samba/private/krb5.conf /etc/krb5.conf

to /etc/samba/smb.conf add line in global: dns forwarder = ******* *******

systemctl start samba-ad-dc

systemctl status samba-ad-dc

smbclient -L localhost -N

smbclient //localhost/netlogon -UAdministrator -c 'ls'

host -t SRV _ldap._tcp.ad.fernir.co.

host -t SRV _kerberos._udp.ad.fernir.co.

host -t A vpsgtmt0.ad.fernir.co

kinit administrator

klist

From https://adamtheautomator.com/samba-active-directory/ :

samba-tool user create alice alice_password88

samba-tool user list

### On windows pc
Get-NetAdapter -Name "*"

Set-DNSClientServerAddress "Ethernet 12" –ServerAddresses ("*************","*******","*******")

Get-DnsClientServerAddress

ping vpsgtmt0.ad.fernir.co

ping ad.fernir.co

Add-Computer -DomainName "ad.fernir.co" -Restart
