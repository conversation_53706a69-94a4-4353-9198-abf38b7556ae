## Default values for Redash.
## This is a YAML-formatted file.

image:
  # image.repository -- Redash image name used for server and worker pods
  repository: redash/redash
  # image.tag -- Redash image [tag](https://hub.docker.com/r/redash/redash/tags)
  tag: 8.0.2.b37747
  # image.pullPolicy - Image pull policy
  pullPolicy: IfNotPresent

# imagePullSecrets -- Name(s) of secrets to use if pulling images from a private registry
imagePullSecrets: []
# nameOverride - Override the autogenerated release name based chart name
nameOverride: ""
fullnameOverride: ""

# env -- Redash global envrionment variables - applied to both server and worker containers.
env:
  PYTHONUNBUFFERED: 0

## Service account and security context configuration
serviceAccount:
  # serviceAccount.create -- Specifies whether a service account should be created
  create: true
  # serviceAccount.annotations -- Annotations to add to the service account
  annotations: {}
  # serviceAccount.name -- The name of the service account to use. If not set and create is true, a name is generated using the fullname template
  name:

## Redash application configuration
redash:
  ## Start primary Redash configuration
  # redash.secretKey -- REQUIRED `REDASH_SECRET_KEY` value. Defaults to ``. Secret key used for data encryption. Stored as a Secret value.
  secretKey:
  # redash.proxiesCount -- `REDASH_PROXIES_COUNT` value. Defaults to `1`.
  proxiesCount:
  # redash.statsdHost -- `REDASH_STATSD_HOST` value. Defaults to `127.0.0.1`.
  statsdHost:
  # redash.statsdPort -- `REDASH_STATSD_PORT` value. Defaults to `8125`.
  statsdPort:
  # redash.statsdPrefix -- `REDASH_STATSD_PREFIX` value. Defaults to `redash`.
  statsdPrefix:
  # redash.statsdUseTags -- `REDASH_STATSD_USE_TAGS` value. Defaults to `false`. Whether to use tags in statsd metrics (influxdb’s format).
  statsdUseTags:
  # redash.celeryBroker -- `REDASH_CELERY_BROKER` value. Defaults to `REDIS_URL`.
  celeryBroker:
  # redash.celeryBackend -- `REDASH_CELERY_BACKEND` value. Defaults to `CELERY_BROKER`.
  celeryBackend:
  # redash.celeryTaskResultExpires -- `REDASH_CELERY_TASK_RESULT_EXPIRES` value. Defaults to `3600 \* 4`. How many seconds to keep celery task results in cache (in seconds).
  celeryTaskResultExpires:
  # redash.queryResultsCleanupEnabled -- `REDASH_QUERY_RESULTS_CLEANUP_ENABLED` value. Defaults to `true`.
  queryResultsCleanupEnabled:
  # redash.queryResultsCleanupCount -- `REDASH_QUERY_RESULTS_CLEANUP_COUNT` value. Defaults to `100`.
  queryResultsCleanupCount:
  # redash.queryResultsCleanupMaxAge -- `REDASH_QUERY_RESULTS_CLEANUP_MAX_AGE` value. Defaults to `7`.
  queryResultsCleanupMaxAge:
  # redash.schemasRefreshQueue -- `REDASH_SCHEMAS_REFRESH_QUEUE` value. Defaults to `celery`. The celery queue for refreshing the data source schemas.
  schemasRefreshQueue:
  # redash.schemasRefreshSchedule -- `REDASH_SCHEMAS_REFRESH_SCHEDULE` value. Defaults to `30`. How often to refresh the data sources schemas (in minutes).
  schemasRefreshSchedule:
  # redash.authType -- `REDASH_AUTH_TYPE` value. Defaults to `api_key`.
  authType:
  # redash.enforceHttps -- `REDASH_ENFORCE_HTTPS` value. Defaults to `false`.
  enforceHttps:
  # redash.invitationTokenMaxAge -- `REDASH_INVITATION_TOKEN_MAX_AGE` value. Defaults to `60 _ 60 _ 24 \* 7`.
  invitationTokenMaxAge:
  # redash.multiOrg -- `REDASH_MULTI_ORG` value. Defaults to `false`.
  multiOrg:
  # redash.googleClientId -- `REDASH_GOOGLE_CLIENT_ID` value. Defaults to ``.
  googleClientId:
  # redash.googleClientSecret -- `REDASH_GOOGLE_CLIENT_SECRET` value. Defaults to ``. Stored as a Secret value.
  googleClientSecret:
  # redash.remoteUserLoginEnabled -- `REDASH_REMOTE_USER_LOGIN_ENABLED` value. Defaults to `false`.
  remoteUserLoginEnabled:
  # redash.remoteUserHeader -- `REDASH_REMOTE_USER_HEADER` value. Defaults to `X-Forwarded-Remote-User`.
  remoteUserHeader:
  # redash.ldapLoginEnabled -- `REDASH_LDAP_LOGIN_ENABLED` value. Defaults to `false`.
  ldapLoginEnabled:
  # redash.ldapUrl -- `REDASH_LDAP_URL` value. Defaults to `None`.
  ldapUrl:
  # redash.ldapBindDn -- `REDASH_LDAP_BIND_DN` value. Defaults to `None`.
  ldapBindDn:
  # redash.ldapBindDnPassword -- `REDASH_LDAP_BIND_DN_PASSWORD` value. Defaults to ``. Stored as a Secret value.
  ldapBindDnPassword:
  # redash.ldapDisplayNameKey -- `REDASH_LDAP_DISPLAY_NAME_KEY` value. Defaults to `displayName`.
  ldapDisplayNameKey:
  # redash.ldapEmailKey -- `REDASH_LDAP_EMAIL_KEY` value. Defaults to `mail`.
  ldapEmailKey:
  # redash.ldapCustomUsernamePrompt -- `REDASH_LDAP_CUSTOM_USERNAME_PROMPT` value. Defaults to `LDAP/AD/SSO username:`.
  ldapCustomUsernamePrompt:
  # redash.ldapSearchTemplate -- `REDASH_LDAP_SEARCH_TEMPLATE` value. Defaults to `(cn=%(username)s)`.
  ldapSearchTemplate:
  # redash.ldapSearchDn -- `REDASH_LDAP_SEARCH_DN` value. Defaults to `REDASH_SEARCH_DN`.
  ldapSearchDn:
  # redash.staticAssetsPath -- `REDASH_STATIC_ASSETS_PATH` value. Defaults to `”../client/dist/”`.
  staticAssetsPath:
  # redash.jobExpiryTime -- `REDASH_JOB_EXPIRY_TIME` value. Defaults to `3600 \* 12`.
  jobExpiryTime:
  # redash.cookieSecret -- REQUIRED `REDASH_COOKIE_SECRET` value. Defaults to ``. Stored as a Secret value.
  cookieSecret:
  # redash.logLevel -- `REDASH_LOG_LEVEL` value. Defaults to `INFO`.
  logLevel:
  # redash.mailServer -- `REDASH_MAIL_SERVER` value. Defaults to `localhost`.
  mailServer:
  # redash.mailPort -- `REDASH_MAIL_PORT` value. Defaults to `25`.
  mailPort:
  # redash.mailUseTls -- `REDASH_MAIL_USE_TLS` value. Defaults to `false`.
  mailUseTls:
  # redash.mailUseSsl -- `REDASH_MAIL_USE_SSL` value. Defaults to `false`.
  mailUseSsl:
  # redash.mailUsername -- `REDASH_MAIL_USERNAME` value. Defaults to `None`.
  mailUsername:
  # redash.mailPassword -- `REDASH_MAIL_PASSWORD` value. Defaults to `None`. Stored as a Secret value.
  mailPassword:
  # redash.mailDefaultSender -- `REDASH_MAIL_DEFAULT_SENDER` value. Defaults to `None`.
  mailDefaultSender:
  # redash.mailMaxEmails -- `REDASH_MAIL_MAX_EMAILS` value. Defaults to `None`.
  mailMaxEmails:
  # redash.mailAsciiAttachments -- `REDASH_MAIL_ASCII_ATTACHMENTS` value. Defaults to `false`.
  mailAsciiAttachments:
  # redash.host -- `REDASH_HOST` value. Defaults to ``.
  host:
  # redash.alertsDefaultMailSubjectTemplate -- `REDASH_ALERTS_DEFAULT_MAIL_SUBJECT_TEMPLATE` value. Defaults to `({state}) {alert_name}`.
  alertsDefaultMailSubjectTemplate:
  # redash.throttleLoginPattern -- `REDASH_THROTTLE_LOGIN_PATTERN` value. Defaults to `50/hour`.
  throttleLoginPattern:
  # redash.limiterStorage -- `REDASH_LIMITER_STORAGE` value. Defaults to `REDIS_URL`.
  limiterStorage:
  # redash.corsAccessControlAllowOrigin -- `REDASH_CORS_ACCESS_CONTROL_ALLOW_ORIGIN` value. Defaults to ``.
  corsAccessControlAllowOrigin:
  # redash.corsAccessControlAllowCredentials -- `REDASH_CORS_ACCESS_CONTROL_ALLOW_CREDENTIALS` value. Defaults to `false`.
  corsAccessControlAllowCredentials:
  # redash.corsAccessControlRequestMethod -- `REDASH_CORS_ACCESS_CONTROL_REQUEST_METHOD` value. Defaults to `GET, POST, PUT`.
  corsAccessControlRequestMethod:
  # redash.corsAccessControlAllowHeaders -- `REDASH_CORS_ACCESS_CONTROL_ALLOW_HEADERS` value. Defaults to `Content-Type`.
  corsAccessControlAllowHeaders:
  # redash.enabledQueryRunners -- `REDASH_ENABLED_QUERY_RUNNERS` value. Defaults to `”,”.join(default_query_runners)`.
  enabledQueryRunners:
  # redash.additionalQueryRunners -- `REDASH_ADDITIONAL_QUERY_RUNNERS` value. Defaults to ``.
  additionalQueryRunners:
  # redash.disabledQueryRunners -- `REDASH_DISABLED_QUERY_RUNNERS` value. Defaults to ``.
  disabledQueryRunners:
  # redash.adhocQueryTimeLimit -- `REDASH_ADHOC_QUERY_TIME_LIMIT` value. Defaults to `None`.
  adhocQueryTimeLimit:
  # redash.enabledDestinations -- `REDASH_ENABLED_DESTINATIONS` value. Defaults to `”,”.join(default_destinations)`.
  enabledDestinations:
  # redash.additionalDestinations -- `REDASH_ADDITIONAL_DESTINATIONS` value. Defaults to ``.
  additionalDestinations:
  # redash.eventReportingWebhooks -- `REDASH_EVENT_REPORTING_WEBHOOKS` value. Defaults to ``.
  eventReportingWebhooks:
  # redash.sentryDsn -- `REDASH_SENTRY_DSN` value. Defaults to ``.
  sentryDsn:
  # redash.allowScriptsInUserInput -- `REDASH_ALLOW_SCRIPTS_IN_USER_INPUT` value. Defaults to `false`. Disable sanitization of text input, allowing full html.
  allowScriptsInUserInput:
  # redash.dashboardRefreshIntervals -- `REDASH_DASHBOARD_REFRESH_INTERVALS` value. Defaults to `60,300,600,1800,3600,43200,86400`.
  dashboardRefreshIntervals:
  # redash.queryRefreshIntervals -- `REDASH_QUERY_REFRESH_INTERVALS` value. Defaults to `60, 300, 600, 900, 1800, 3600, 7200, 10800, 14400, 18000, 21600, 25200, 28800, 32400, 36000, 39600, 43200, 86400, 604800, 1209600, 2592000`.
  queryRefreshIntervals:
  # redash.passwordLoginEnabled -- `REDASH_PASSWORD_LOGIN_ENABLED` value. Defaults to `true`.
  passwordLoginEnabled:
  # redash.samlMetadataUrl -- `REDASH_SAML_METADATA_URL` value. Defaults to ``.
  samlMetadataUrl:
  # redash.samlEntityId -- `REDASH_SAML_ENTITY_ID` value. Defaults to ``.
  samlEntityId:
  # redash.samlNameidFormat -- `REDASH_SAML_NAMEID_FORMAT` value. Defaults to ``.
  samlNameidFormat:
  # redash.dateFormat -- `REDASH_DATE_FORMAT` value. Defaults to `DD/MM/YY`.
  dateFormat:
  # redash.jwtLoginEnabled -- `REDASH_JWT_LOGIN_ENABLED` value. Defaults to `false`.
  jwtLoginEnabled:
  # redash.jwtAuthIssuer -- `REDASH_JWT_AUTH_ISSUER` value. Defaults to ``.
  jwtAuthIssuer:
  # redash.jwtAuthPublicCertsUrl -- `REDASH_JWT_AUTH_PUBLIC_CERTS_URL` value. Defaults to ``.
  jwtAuthPublicCertsUrl:
  # redash.jwtAuthAudience -- `REDASH_JWT_AUTH_AUDIENCE` value. Defaults to ``.
  jwtAuthAudience:
  # redash.jwtAuthAlgorithms -- `REDASH_JWT_AUTH_ALGORITHMS` value. Defaults to `HS256,RS256,ES256`.
  jwtAuthAlgorithms:
  # redash.jwtAuthCookieName -- `REDASH_JWT_AUTH_COOKIE_NAME` value. Defaults to ``.
  jwtAuthCookieName:
  # redash.jwtAuthHeaderName -- `REDASH_JWT_AUTH_HEADER_NAME` value. Defaults to ``.
  jwtAuthHeaderName:
  # redash.featureShowQueryResultsCount -- `REDASH_FEATURE_SHOW_QUERY_RESULTS_COUNT` value. Defaults to `true`. Disable/enable showing count of query results in status.
  featureShowQueryResultsCount:
  # redash.versionCheck -- `REDASH_VERSION_CHECK` value. Defaults to `true`.
  versionCheck:
  # redash.featureDisableRefreshQueries -- `REDASH_FEATURE_DISABLE_REFRESH_QUERIES` value. Defaults to `false`. Disable scheduled query execution.
  featureDisableRefreshQueries:
  # redash.featureShowPermissionsControl -- `REDASH_FEATURE_SHOW_PERMISSIONS_CONTROL` value. Defaults to `false`.
  featureShowPermissionsControl:
  # redash.featureAllowCustomJsVisualizations -- `REDASH_FEATURE_ALLOW_CUSTOM_JS_VISUALIZATIONS` value. Defaults to `false`.
  featureAllowCustomJsVisualizations:
  # redash.featureDumbRecents -- `REDASH_FEATURE_DUMB_RECENTS` value. Defaults to `false`.
  featureDumbRecents:
  # redash.featureAutoPublishNamedQueries -- `REDASH_FEATURE_AUTO_PUBLISH_NAMED_QUERIES` value. Defaults to `true`.
  featureAutoPublishNamedQueries:
  # redash.bigqueryHttpTimeout -- `REDASH_BIGQUERY_HTTP_TIMEOUT` value. Defaults to `600`.
  bigqueryHttpTimeout:
  # redash.schemaRunTableSizeCalculations -- `REDASH_SCHEMA_RUN_TABLE_SIZE_CALCULATIONS` value. Defaults to `false`.
  schemaRunTableSizeCalculations:
  # redash.webWorkers -- `REDASH_WEB_WORKERS` value. Defaults to `4`. How many processes will gunicorn spawn to handle web requests.
  webWorkers:
  ## End primary Redash configuration
  # redash.existingSecret -- Name of existing secret to use instead of either the values above
  ## This secret must contain keys matching the items marked "Stored as a Secret value" above.
  # existingSecret:

## Configuration for Redash web server
server:
  # server.httpPort -- Server container port (only useful if you are using a customized image)
  httpPort: 5000

  # server.env -- Redash server specific envrionment variables
  # Don't use this for variables that are in the configuration above, however.
  env: {}

  # server.replicaCount -- Number of server pods to run
  replicaCount: 1

  # server.resources -- Server resource requests and limits [ref](http://kubernetes.io/docs/user-guide/compute-resources/)
  resources:
    # limits:
    #   cpu: 500m
    #   memory: 3Gi
    # requests:
    #   cpu: 100m
    #  memory: 500Mi

  # server.podSecurityContext -- Security contexts for server pod assignment [ref](https://kubernetes.io/docs/tasks/configure-pod-container/security-context/)
  podSecurityContext: {}
  securityContext: {}

  # server.nodeSelector -- Node labels for server pod assignment [ref](https://kubernetes.io/docs/user-guide/node-selection/)
  nodeSelector: {}

  # server.tolerations -- Tolerations for server pod assignment [ref](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/)
  tolerations: []

  # server.affinity -- Affinity for server pod assignment [ref](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity)
  affinity: {}

service:
  # service.type -- Kubernetes Service type
  type: ClusterIP
  # service.port -- Service external port
  port: 80

ingress:
  # ingress.enabled -- Enable ingress controller resource
  enabled: false
  # ingress.annotations -- Ingress annotations configuration
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  # ingress.hosts -- Ingress resource hostnames and path mappings
  hosts:
    - host: chart-example.local
      paths: []
  # ingress.tls -- Ingress TLS configuration
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

## Configuration for Redash ad-hoc workers
adhocWorker:
  # adhocWorker.env -- Redash ad-hoc worker specific envrionment variables.
  env:
    QUEUES: "queries,celery"
    WORKERS_COUNT: 2

  # adhocWorker.replicaCount -- Number of ad-hoc worker pods to run
  replicaCount: 1

  # adhocWorker.resources -- Ad-hoc worker resource requests and limits [ref](http://kubernetes.io/docs/user-guide/compute-resources/)
  resources:
    # limits:
    #   cpu: 500m
    #   memory: 3Gi
    # requests:
    #   cpu: 100m
    #  memory: 500Mi

  # adhocWorker.podSecurityContext -- Security contexts for ad-hoc worker pod assignment [ref](https://kubernetes.io/docs/tasks/configure-pod-container/security-context/)
  podSecurityContext: {}
  securityContext: {}

  # adhocWorker.nodeSelector -- Node labels for ad-hoc worker pod assignment [ref](https://kubernetes.io/docs/user-guide/node-selection/)
  nodeSelector: {}

  # adhocWorker.tolerations -- Tolerations for ad-hoc worker pod assignment [ref](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/)
  tolerations: []

  # adhocWorker.affinity -- Affinity for ad-hoc worker pod assignment [ref](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity)
  affinity: {}

## Configuration for Redash scheduled workers
scheduledWorker:
  # scheduledWorker.env -- Redash scheduled worker specific envrionment variables.
  env:
    QUEUES: "scheduled_queries"
    WORKERS_COUNT: 2

  # scheduledWorker.replicaCount -- Number of scheduled worker pods to run
  replicaCount: 1

  # scheduledWorker.resources -- Scheduled worker resource requests and limits [ref](http://kubernetes.io/docs/user-guide/compute-resources/)
  resources:
    # limits:
    #   cpu: 500m
    #   memory: 3Gi
    # requests:
    #   cpu: 100m
    #  memory: 500Mi

  # scheduledWorker.podSecurityContext -- Security contexts for scheduled worker pod assignment [ref](https://kubernetes.io/docs/tasks/configure-pod-container/security-context/)
  podSecurityContext: {}
  securityContext: {}

  # scheduledWorker.nodeSelector -- Node labels for scheduled worker pod assignment [ref](https://kubernetes.io/docs/user-guide/node-selection/)
  nodeSelector: {}

  # scheduledWorker.tolerations -- Tolerations for scheduled worker pod assignment [ref](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/)
  tolerations: []

  # scheduledWorker.affinity -- Affinity for scheduled worker pod assignment [ref](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity)
  affinity: {}

# externalPostgreSQL -- External PostgreSQL configuration. To use an external PostgreSQL instead of the automatically deployed postgresql chart: set postgresql.enabled to false then uncomment and configure the externalPostgreSQL connection URL.
# externalPostgreSQL: ********************************/database

## Configuration values for the postgresql dependency. This PostgreSQL instance is used by default for all Redash state storage [ref](https://github.com/kubernetes/charts/blob/master/stable/postgresql/README.md)
postgresql:
  # postgresql.enabled -- Whether to deploy a PostgreSQL server to satisfy the applications database requirements. To use an external PostgreSQL set this to false and configure the externalPostgreSQL parameter.
  enabled: true
  image:
    # postgresql.image.tag -- Bitnami supported version close to the one specified in Redash [setup docker-compose.yml](https://github.com/getredash/setup/blob/master/data/docker-compose.yml)
    tag: "9.6.17-debian-10-r3"
  # postgresql.postgresqlUsername -- PostgreSQL username for redash user (when postgresql chart enabled)
  postgresqlUsername: redash
  # postgresql.postgresqlPassword -- REQUIRED: PostgreSQL password for redash user (when postgresql chart enabled)
  postgresqlPassword:
  # postgresql.postgresqlDatabase -- PostgreSQL database name (when postgresql chart enabled)
  postgresqlDatabase: redash
  service:
    type: ClusterIP
    port: 5432
  persistence:
    # postgresql.persistence.enabled -- Use a PVC to persist PostgreSQL data (when postgresql chart enabled)
    enabled: true
    # postgresql.persistence.storageClass - Storage Class for PostgreSQL backing PVC. If undefined (the default) or set to null, no storageClassName spec is set, choosing the default provisioner.  (gp2 on AWS, standard on GKE, AWS & OpenStack).
    storageClass: ""
    # postgresql.persistence.accessMode -- Use PostgreSQL volume as ReadOnly or ReadWrite
    accessMode: ReadWriteOnce
    # postgresql.persistence.size -- PVC Storage Request size for PostgreSQL volume
    size: 10Gi
    # postgresql.persistence.existingClaim -- Provide an existing PostgreSQL PersistentVolumeClaim
    # existingClaim: ""

# externalRedis -- External Redis configuration. To use an external Redis instead of the automatically deployed redis chart: set redis.enabled to false then uncomment and configure the externalRedis connection URL.
# externalRedis: redis://user:pass@host:6379/database

## Configuration values for the redis dependency. This Redis instance is used by default for caching and temporary storage [ref](https://github.com/kubernetes/charts/blob/master/stable/redis/README.md)
redis:
  # redis.enabled -- Whether to deploy a Redis server to satisfy the applications database requirements. To use an external Redis set this to false and configure the externalRedis parameter.
  enabled: true
  # redis.password -- If the password is not specified, a random password will be generated (when redis chart enabled)
  # password:
  # redis.databaseNumber -- Redis database number to use (when redis chart enabled)
  databaseNumber: 0
  master:
    # redis.master.port -- Redis master port to use (when redis chart enabled)
    port: 6379
  cluster:
    # redis.databaseNumber -- Enable Redis clustering (when redis chart enabled)
    enabled: false
