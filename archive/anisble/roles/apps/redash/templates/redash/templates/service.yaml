apiVersion: v1
kind: Service
metadata:
  name: {{ include "redash.fullname" . }}
  labels:
    {{- include "redash.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.server.httpPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "redash.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: server