apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "redash.adhocWorker.fullname" . }}
  labels:
    {{- include "redash.labels" . | nindent 4 }}
    app.kubernetes.io/component: adhocworker
spec:
  replicas: {{ .Values.adhocWorker.replicaCount }}
  selector:
    matchLabels:
      {{- include "redash.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: adhocworker
  template:
    metadata:
      labels:
        {{- include "redash.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: adhocworker
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      serviceAccountName: {{ include "redash.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "redash.name" . }}-adhocworker
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh"]
          args: ["-c", ". /config/dynamicenv.sh && /app/bin/docker-entrypoint scheduler"]
          volumeMounts:
            - name: config
              mountPath: /config
          env:
          {{- include "redash.env" . | nindent 12 }}
          {{- range $key, $value := .Values.adhocWorker.env }}
            - name: "{{ $key }}"
              value: "{{ $value }}"
          {{- end }}
          resources:
{{ toYaml .Values.adhocWorker.resources | indent 12 }}
      volumes:
        - name: config
          configMap:
            name: {{ include "redash.fullname" . }}
    {{- if .Values.adhocWorker.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.adhocWorker.nodeSelector | indent 8 }}
    {{- end }}
    {{- with .Values.adhocWorker.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.adhocWorker.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
