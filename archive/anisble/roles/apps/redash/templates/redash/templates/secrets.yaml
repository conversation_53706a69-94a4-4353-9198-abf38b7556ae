{{- if not .Values.redash.existingSecret -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "redash.fullname" . }}
  labels:
    {{- include "redash.labels" . | nindent 4 }}
type: Opaque
data:
  ## Start primary Redash configuration
  {{ $null := required "A value for one of the following variables is required: redash.secretKey (secure random value), redash.existingSecret (secret name)" (or .Values.redash.secretKey .Values.redash.existingSecret) }}
  secretKey: {{ default "" .Values.redash.secretKey | b64enc | quote }}
  googleClientSecret: {{ default "" .Values.redash.googleClientSecret | b64enc | quote }}
  ldapBindDnPassword: {{ default "" .Values.redash.ldapBindDnPassword | b64enc | quote }}
  {{ $null := required "A value for one of the following variables is required: redash.cookieSecret (secure random value), redash.existingSecret (secret name)" (or .Values.redash.cookieSecret .Values.redash.existingSecret) }}
  cookieSecret: {{ default "" .Values.redash.cookieSecret | b64enc | quote }}
  mailPassword: {{ default "" .Values.redash.mailPassword | b64enc | quote }}
  ## End primary Redash configuration
{{- end -}}