redash:
  cookieSecret: 9+dxDylv16e+0FjHIxBeWEeNyuV+J7LYBN8AwiC7oTE=
  secretKey: ekJiGRss0HiwhXEJQnU5t30D6cuFtHnzDTmzMzPQYho=
externalRedis: redis://redis-master.redis.svc.cluster.local:6379
externalPostgreSQL: postgresql://postgres:<EMAIL>:5432/redash
postgresql:
  enabled: false
redis:
  enabled: false
server:
  resources:
    requests:
      cpu: '0'
      memory: 500Mi
    limits:
      cpu: '0'
      memory: 1Gi
  env:
    REDASH_WEB_WORKERS: 1
service:
  type: NodePort
  port: 80
  nodePort: 31005
