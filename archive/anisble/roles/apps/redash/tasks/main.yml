#todo bt is helm install needed here?
- name: Install helm to localhost
  become: yes
  unarchive:
    src: https://get.helm.sh/helm-v3.3.1-linux-amd64.tar.gz
    dest: /usr/local/bin
    extra_opts:
      - --strip=1
      - --wildcards
      - '*/helm'
    remote_src: true
  delegate_to: localhost

- name: Add stable repo to helm
  shell: helm repo add stable https://kubernetes-charts.storage.googleapis.com/

- name: Update redash chart dependencies
  shell: helm dep update {{redash_templates_dir}}/redash
  delegate_to: localhost

- name: Package redash
  shell: helm package {{redash_templates_dir}}/redash
  delegate_to: localhost

- name: Copy package
  copy:
    src: "{{ansible_dir}}/redash-2.0.0-beta.6.tgz"
    dest: "/tmp/redash-2.0.0-beta.6.tgz"

- name: Get postgresql-password from postgresql-secret
  shell: "kubectl get secrets/postgresql-secret -n apps -o jsonpath='{.data.postgresql-password}' | base64 --decode"
  register: psql_password

- set_fact:
    error_text: "ERROR:  database \"redash\" already exists"

- name: Create redash db
  shell: "kubectl exec --namespace=postgres -it postgres-postgresql-0 -- psql 'user=postgres password={{psql_password.stdout}} host=localhost port=5432' -c 'CREATE DATABASE \"redash\";'"
  register: psql_res
  failed_when:
     - "error_text not in psql_res.stdout_lines"
  retries: 10
  delay: 10
  until:
     - "(error_text in psql_res.stdout_lines) or (error_text in psql_res.stderr_lines)"

- name: Install redash
  shell: helm install redash --namespace apps -f - /tmp/redash-2.0.0-beta.6.tgz
  args:
    stdin: "{{ lookup('file', '{{redash_templates_dir}}/redash-overrides.yml')}}"

- name: Set redash node port
  shell: "kubectl patch svc redash --namespace apps -p \'{\"spec\": {\"ports\": [{\"name\": \"http\",\"protocol\": \"TCP\",\"port\": 80,\"targetPort\": 5000,\"nodePort\": 31005}]}}\'"

- name: Set periodSeconds in livenessProbe to 90 (otherwise too much cpu)
  shell: "kubectl patch deployment redash --namespace apps -p \'{\"spec\": {\"template\": {\"spec\": {\"containers\": [{\"name\": \"redash-server\",\"livenessProbe\": {\"periodSeconds\": 90}}]}}}}\'"

#todo bs redash secret investigate
