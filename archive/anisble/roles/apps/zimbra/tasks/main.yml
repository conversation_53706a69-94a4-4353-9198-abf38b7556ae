- name: Create zimbra pv directory
  file:
    path: "{{cluster_data_dir}}/zimbra-pv"
    state: directory

- name: Create local persistent volume (zimbra pv)
  shell: kubectl apply --namespace zimbra -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/zimbra/templates/zimbra-pv-pvc.yml')}}"

- name: Deploy Zimbra
  shell: kubectl apply --namespace zimbra -f -
  args:
    stdin: "{{ lookup('file', '{{apps_dir}}/zimbra/deployment.yml')}}"

- name: Create Zimbra service
  shell: kubectl apply --namespace zimbra -f -
  args:
    stdin: "{{ lookup('file', '{{apps_dir}}/zimbra/service.yml')}}"
