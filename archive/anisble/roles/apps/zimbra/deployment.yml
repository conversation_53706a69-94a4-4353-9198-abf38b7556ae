apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zimbra
  name: zimbra
  namespace: zimbra
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zimbra
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: zimbra
      annotations:
        #todo fd sec zimbra check unconfined apparmor
        container.apparmor.security.beta.kubernetes.io/zimbra: unconfined
        container.seccomp.security.alpha.kubernetes.io/zimbra: unconfined
    spec:
      restartPolicy: Always
      terminationGracePeriodSeconds: 180
      containers:
      - name: zimbra
        image: griffinplus/zimbra
        imagePullPolicy: Always
        env:
        - name: EXTERNAL_HOST_FQDN
          value: zimbra.ks.com
        - name: MAIL_DOMAINS
          value: ks.com
        volumeMounts:
        - name: zimbra
          mountPath: /data
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
            - SYS_ADMIN
            - SYS_PTRACE
      volumes:
      - name: zimbra
        persistentVolumeClaim:
          claimName: zimbra-pvc
