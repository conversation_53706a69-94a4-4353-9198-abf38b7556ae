apiVersion: v1
kind: Service
metadata:
  name: zimbra-public
  namespace: zimbra
spec:
  selector:
    app: zimbra
  type: NodePort
  ports:
    - name: smtp
      port: 25
      nodePort: 31011
      protocol: TCP
    - name: http
      port: 8080
      nodePort: 31010
      protocol: TCP
    - name: pop3
      port: 110
      nodePort: 31012
      protocol: TCP
    - name: imap
      port: 143
      nodePort: 31013
      protocol: TCP
    - name: smtps
      port: 465
      nodePort: 31014
      protocol: TCP
    - name: submission
      port: 587
      nodePort: 31015
      protocol: TCP
    - name: imaps
      port: 993
      nodePort: 31016
      protocol: TCP
    - name: pop3s
      port: 995
      nodePort: 31017
      protocol: TCP
    - name: xmpp
      port: 5222
      nodePort: 31018
      protocol: TCP
    - name: xmpp-legacy
      port: 5223
      nodePort: 31019
      protocol: TCP
    - name: adminpanel
      port: 9071
      nodePort: 31020
      protocol: TCP
