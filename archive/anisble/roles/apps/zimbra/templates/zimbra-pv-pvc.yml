apiVersion: v1
kind: PersistentVolume
metadata:
  name: zimbra-pv
  labels:
    app: zimbra
spec:
  capacity:
    storage: {{volume_xl}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: {{cluster_data_dir}}/zimbra-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
  claimRef:
    kind: PersistentVolumeClaim
    name: zimbra-pvc
    namespace: zimbra
---

kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: zimbra-pvc
  namespace: zimbra
  labels:
    app: zimbra
spec:
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{volume_xl}}
  storageClassName: local-storage
  selector:
    matchLabels:
      app: zimbra
