POD=`kubectl get pods -n zimbra -o name | cut -f2 -d '/'`
kubectl exec -it -n zimbra $POD -- bash

chroot /data /bin/bash
cd /app
./install-zimbra.sh

do not install zimbra-dnscache & zimbra-imapd

zimbra-store:
  - Admin Password set
  - Web server mode: both

Common configuration:
  - Secure interprocess communications: no

zimbra-proxy:
  - Enable strict server name enforcement? no
  - Enable HTTP[S] Proxy: FALSE

## Set proxy for admin console on first deploy
su - zimbra
/opt/zimbra/libexec/zmproxyconfig -e -w -C -H 'zimbra.ks.com'
zmproxyctl restart
