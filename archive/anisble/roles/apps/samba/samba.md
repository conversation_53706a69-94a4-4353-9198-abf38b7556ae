# Manual parts

[Samba (artifacthub)](https://artifacthub.io/packages/helm/k8s-at-home/samba) ( Package not found.)

[A simple in-cluster Samba server (github)](https://github.com/k8s-at-home/charts/tree/master/charts/stable/samba)

## Delete

```bash
helm uninstall -n samba samba
```

## ToDo

uncomment things in config
ldap and samba

## Worked

[Install and Configure Samba](https://ubuntu.com/tutorials/install-and-configure-samba#4-setting-up-user-accounts-and-connecting-to-share)

[Vagrant mount guest samba shares](https://wirywolf.com/2014/03/vagrant-mount-guest-samba-shares.html)

```text
wins support = yes
dns proxy = no

min protocol = SMB2

\\194.61.53.228\sambashare
```

```bash
smbclient -L 127.0.0.1 -U km -p 445
smbclient -p 445 -L 194.61.53.228 -U km
smbclient -p 445 -U km \\\\194.61.53.228\\sambashare
```

net use \\10.251.0.0\foo /d

## Ldap

[Samba - OpenLDAP Backend](https://ubuntu.com/server/docs/samba-openldap-backend)

[Configure a Centos 7 Samba Server to Use a Secure LDAP Authentication](https://7thzero.com/blog/configure-centos-7-samba-server-use-secure-ldap-authentication)

[Samba standalone + OpenLDAP](https://spredzy.wordpress.com/2013/08/30/samba-standalone-openldap/)

[Install and configure a LDAP directory server (github)](https://github.com/nodiscc/xsrv/tree/master/roles/openldap)

[samba](https://github.dev/nodiscc/xsrv/tree/master/roles/samba)

### Local tutorial

[Creating a Basic authenticated access smb.conf File](https://wiki.samba.org/index.php/Setting_up_Samba_as_a_Standalone_Server#Creating_a_Basic_authenticated_access_smb.conf_File)

pwd demoUser Propel!cask4Mary


### Local config

```text
[global]
        log file = /var/log/samba/%m
        log level = 1
        server role = standalone server
        bind interfaces only = yes
        interfaces = lo tun0
        wins support = yes
        dns proxy = no
        server min protocol = SMB2
        tls enabled = no
        disable netbios = yes
        smb ports = 445

[demo]
        # This share requires authentication to access
        path = /srv/samba/demo/
        read only = no
        inherit permissions = yes
```
