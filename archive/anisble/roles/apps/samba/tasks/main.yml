- name: Create namespace
  shell: kubectl create namespace samba --dry-run=client -o yaml | kubectl apply -f -

- name: Add helm repo
  shell: helm repo add k8s-at-home https://k8s-at-home.com/charts/

- name: Update helm repo
  shell: helm repo update

- name: Deploy
  shell: helm install samba --namespace samba -f - k8s-at-home/samba --version 6.2.2
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/samba/templates/samba-overrides.yml')}}"
