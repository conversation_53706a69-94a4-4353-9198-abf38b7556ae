- name: Install apt-transport-https
  become: yes
  apt:
    name: "{{packages}}"
    state: present
    update_cache: yes
  vars:
    packages:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common

- name: Add Docker APT GPG key
  become: yes
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg

- name: Add Docker APT repository
  become: yes
  apt_repository:
    repo: "deb https://download.docker.com/linux/ubuntu focal stable"
    state: present
    filename: 'docker'

- name: Install docker engine (Debian/Ubuntu)
  become: yes
  apt:
    update_cache: yes
    name: "docker-ce={{ docker_version }}*"
    state: present

- name: Hold docker version
  become: yes
  dpkg_selections:
    name: docker-ce
    selection: hold

- name: Copy Docker engine service file
  become: yes
  register: change_docker
  template:
    src: "docker.service.j2"
    dest: "/lib/systemd/system/docker.service"
    owner: root
    group: root
    mode: 0750

- name: Copy Docker environment config file
  become: yes
  template: src=docker.j2 dest=/etc/default/docker

- name: Add any insecure registries to Docker config
  become: yes
  when: insecure_registries is defined and insecure_registries | length > 0
  lineinfile: dest=/etc/default/docker regexp=^DOCKER_OPTS= line=DOCKER_OPTS="{% for reg in insecure_registries %}--insecure-registry={{ reg }} {% endfor %} --data-root='/var/lib/docker'"

- name: Add registry to Docker config
  become: yes
  when: add_registry is defined and add_registry | length > 0
  lineinfile: dest=/etc/default/docker regexp=^ADD_REGISTRY= line=ADD_REGISTRY="{% for reg in add_registry %}--add-registry={{ reg }} {% endfor %}"

- name: Configure Docker daemon
  become: yes
  register: change_docker
  template:
    src: "{{ kube_dir }}/docker/templates/docker-daemon-config.json"
    dest: "/etc/docker/daemon.json"
    owner: root
    group: root
    mode: 0750

- name: Enable and check Docker service
  become: yes
  systemd:
    name: docker
    daemon_reload: yes
    state: started
    enabled: yes
  register: started_docker

- name: Set TasksMax limit to infinity in Docker service
  become: yes
  shell: systemctl set-property docker.service TasksMax=infinity

- name: Restart Docker
  become: yes
  service:
    name: docker
    state: restarted

- name: Insert docker system prune cron job to crontab
  ansible.builtin.cron:
    name: "docker system prune"
    minute: "0"
    hour: "12"
    weekday: "6"
    job: '[ $(expr $(date +\%W) \% 2) -eq 1 ] && /usr/bin/docker system prune -af' #biweekly
