[Unit]
Description=Docker Engine
After=network.target

[Service]
Type=notify
EnvironmentFile=-/etc/default/docker
ExecStartPost=/sbin/iptables -I FORWARD -s 0.0.0.0/0 -j ACCEPT
ExecStart=/usr/bin/dockerd -H fd:// \
  $OPTIONS \
  $DOCKER_STORAGE_OPTIONS \
  $DOCKER_OPTS \
  $DOCKER_NETWORK_OPTIONS \
  $ADD_REGISTRY \
  $BLOCK_REGISTRY \
  $INSECURE_REGISTRY

ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-failure
LimitNOFILE=1048576
LimitNPROC=infinity
LimitCORE=infinity
TimeoutStartSec=0
Delegate=yes
KillMode=process

[Install]
WantedBy=multi-user.target
