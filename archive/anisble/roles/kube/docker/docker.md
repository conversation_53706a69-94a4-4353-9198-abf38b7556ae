### Configure docker for registry on km and km-staging

docker-machine create default --engine-insecure-registry 192.169.35.10:30500 --engine-insecure-registry 46.175.149.91:30500 --engine-insecure-registry 194.61.53.228:30500 --virtualbox-disk-size 25060 --virtualbox-memory 2500 --virtualbox-cpu-count 2

## remove all unused data
sudo docker system prune -a

## remove all stopped containers
docker rm $(docker ps -a -q)

## Configure docker-machine
Run `dma ls` to see that your `default` machine is running. If it is not running run `dma start default`.

Run `dma env` and copy last from output line without # symbol, will be something like this `eval $("C:\Program Files\Docker Toolbox\docker-machine.exe" env)`. And then execute this command.

To check that it worked run `docker ps`, if no errors then it worked.

