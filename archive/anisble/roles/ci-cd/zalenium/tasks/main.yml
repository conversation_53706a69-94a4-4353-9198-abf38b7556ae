- name: Add zalenium repo
  shell: helm repo add zalenium-github https://raw.githubusercontent.com/zalando/zalenium/3.141.59z/charts/zalenium

- name: Deploy zalenium
  shell: helm install zalenium --namespace ci-cd -f - zalenium-github/zalenium
  args:
    stdin: "{{ lookup('file', '{{zalenium_dir}}/templates/zalenium-overrides.yml')}}"

- name: Set zalenium node port
  shell: "kubectl patch svc zalenium --namespace ci-cd -p '{\"spec\": {\"ports\": [{\"name\": \"hub\",\"protocol\": \"TCP\",\"port\": 80,\"targetPort\": 4444,\"nodePort\": 31001}]}}'"
