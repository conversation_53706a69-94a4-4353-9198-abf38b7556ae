hub:
  resources:
    requests:
      cpu: '0'
      memory: '0'
    limits:
      cpu: '500m'
      memory: 1024Mi
  desiredContainers: 1
  maxDockerSeleniumContainers: 2
  maxTestSessions: 100
  newSessionWaitTimeout: 800000
  cpuRequest: '0'
  cpuLimit: '500m'
  memRequest: '0'
  memLimit: '1024Mi'
  sendAnonymousUsageInfo: false
  screenWidth: 1280
  screenHeight: 720
  seleniumImageName: elgalu/selenium:3.141.59-p47
