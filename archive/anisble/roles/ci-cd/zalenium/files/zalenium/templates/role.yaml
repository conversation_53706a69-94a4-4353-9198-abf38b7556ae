{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: {{ if .Values.rbac.clusterWideAccess }}"ClusterRole"{{ else }}"Role"{{ end }}
metadata:
  name: {{ template "zalenium.fullname" . }}
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  attributeRestrictions: null
  resources:
  - pods
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - watch
- apiGroups:
  - ""
  attributeRestrictions: null
  resources:
  - pods/exec
  verbs:
  - create
  - get
{{- end -}}
