{{- if not .Values.hub.openshift.deploymentConfig.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "zalenium.fullname" . }}
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "zalenium.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    {{- include "zalenium.podTemplate" . | nindent 4 }}
{{- end -}}