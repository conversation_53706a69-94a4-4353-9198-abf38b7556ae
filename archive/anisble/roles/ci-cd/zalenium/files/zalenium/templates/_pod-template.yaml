{{- define "zalenium.podTemplate" -}}
metadata:
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
  annotations:
    {{- toYaml .Values.hub.podAnnotations | nindent 4 }}
spec:
  securityContext:
    {{- toYaml .Values.hub.podSecurityContext | nindent 4 }}
  {{- if .Values.hub.pullSecrets }}
  imagePullSecrets:
    - name: {{ .Values.hub.imagePullSecret }}
  {{- end}}
  volumes:
    - name: {{ template "zalenium.fullname" . }}-videos
    {{- if .Values.persistence.video.enabled }}
      {{- if and (.Values.persistence.video.useExisting) (not (empty .Values.persistence.video.name))}}
      persistentVolumeClaim:
        claimName: {{ .Values.persistence.video.name }}
      {{- else }}
      persistentVolumeClaim:
        claimName: {{ template "zalenium.fullname" . }}-videos
      {{- end }}
    {{- else }}
      emptyDir: {}
    {{- end }}
    - name: {{ template "zalenium.fullname" . }}-data
    {{- if .Values.persistence.data.enabled }}
      {{- if and (.Values.persistence.data.useExisting) (not (empty .Values.persistence.data.name))}}
      persistentVolumeClaim:
        claimName: {{ .Values.persistence.data.name }}
      {{- else }}
      persistentVolumeClaim:
        claimName: {{ template "zalenium.fullname" . }}-data
      {{- end }}
    {{- else }}
      emptyDir: {}
    {{- end }}
  containers:
    - name: {{ .Chart.Name }}
      image: "{{ .Values.hub.image }}:{{ .Values.hub.tag }}"
      imagePullPolicy: {{ .Values.hub.pullPolicy }}
      securityContext:
        {{- toYaml .Values.hub.containerSecurityContext | nindent 8 }}
      ports:
        - containerPort: {{ .Values.hub.port }}
          protocol: TCP
      livenessProbe:
        httpGet:
          path: {{ if .Values.ingress.path }}{{ .Values.ingress.path }}{{ end }}/status
          port: {{ .Values.hub.port }}
          {{- if eq true .Values.hub.basicAuth.enabled }}
          httpHeaders:
          - name: Authorization
            value: Basic {{ template "basicAuth.b64" . }}
          {{- end}}
        initialDelaySeconds: 90
        periodSeconds: 5
        timeoutSeconds: {{ .Values.hub.livenessTimeout }}
      readinessProbe:
        httpGet:
          path: {{ if .Values.ingress.path }}{{ .Values.ingress.path }}{{ end }}/status
          port: {{ .Values.hub.port }}
          {{- if eq true .Values.hub.basicAuth.enabled }}
          httpHeaders:
          - name: Authorization
            value: Basic {{ template "basicAuth.b64" . }}
          {{- end}}
        timeoutSeconds: {{ .Values.hub.readinessTimeout }}
      env:
        - name: ZALENIUM_KUBERNETES_CPU_REQUEST
          value: {{ .Values.hub.cpuRequest | quote }}
        - name: ZALENIUM_KUBERNETES_CPU_LIMIT
          value: {{ .Values.hub.cpuLimit | quote }}
        - name: ZALENIUM_KUBERNETES_MEMORY_REQUEST
          value: {{ .Values.hub.memRequest | quote }}
        - name: ZALENIUM_KUBERNETES_MEMORY_LIMIT
          value: {{ .Values.hub.memLimit | quote }}
        {{- if .Values.hub.basicAuth.enabled }}
        - name: GRID_USER
          valueFrom:
            secretKeyRef:
              key: zalenium-user
              name: {{ template "zalenium.fullname" . }}
        - name: GRID_PASSWORD
          valueFrom:
            secretKeyRef:
              key: zalenium-password
              name: {{ template "zalenium.fullname" . }}
        {{- end }}
        - name: DESIRED_CONTAINERS
          value: {{ .Values.hub.desiredContainers | quote }}
        - name: MAX_DOCKER_SELENIUM_CONTAINERS
          value: {{ .Values.hub.maxDockerSeleniumContainers | quote }}
        - name: SELENIUM_IMAGE_NAME
          value: {{ .Values.hub.seleniumImageName | quote }}
        - name: VIDEO_RECORDING_ENABLED
          value: {{ .Values.hub.videoRecordingEnabled | quote }}
        - name: SCREEN_WIDTH
          value: {{ .Values.hub.screenWidth | quote }}
        - name: SCREEN_HEIGHT
          value: {{ .Values.hub.screenHeight | quote }}
        - name: MAX_TEST_SESSIONS
          value: {{ .Values.hub.maxTestSessions | quote }}
        - name: NEW_SESSION_WAIT_TIMEOUT
          value: {{ .Values.hub.newSessionsWaitTimeout | quote }}
        - name: DEBUG_ENABLED
          value: {{ .Values.hub.debugEnabled | quote }}
        - name: SEND_ANONYMOUS_USAGE_INFO
          value: {{ .Values.hub.sendAnonymousUsageInfo | quote }}
        - name: TZ
          value: {{ .Values.hub.timeZone | quote }}
        - name: KEEP_ONLY_FAILED_TESTS
          value: {{ .Values.hub.keepOnlyFailedTests | quote }}
        - name: RETENTION_PERIOD
          value: {{ .Values.hub.retentionPeriod | quote }}
        {{- if .Values.ingress.path }}
        - name: CONTEXT_PATH
          value: {{ .Values.ingress.path | quote }}
        {{- end }}
        {{- if .Values.ingress.maxBodySize }}
        - name: NGINX_MAX_BODY_SIZE
          value: {{ .Values.ingress.maxBodySize | quote }}
        {{- end }}
        {{- if .Values.hub.sauceLabsEnabled }}
        - name: SAUCE_LABS_ENABLED
          value: {{ .Values.hub.sauceLabsEnabled | quote }}
        - name: SAUCE_USERNAME
          value: {{ .Values.hub.sauceUserName | quote }}
        - name: SAUCE_ACCESS_KEY
          value: {{ .Values.hub.sauceAccessKey | quote }}
        {{- end }}
        {{- if .Values.hub.browserStackEnabled }}
        - name: BROWSER_STACK_ENABLED
          value: {{ .Values.hub.browserStackEnabled | quote }}
        - name: BROWSER_STACK_USER
          value: {{ .Values.hub.browserStackUser | quote }}
        - name: BROWSER_STACK_KEY
          value: {{ .Values.hub.browserStackKey | quote }}
        {{- end }}
        {{- if .Values.hub.testingBotEnabled }}
        - name: TESTINGBOT_ENABLED
          value: {{ .Values.hub.testingBotEnabled | quote }}
        - name: TESTINGBOT_KEY
          value: {{ .Values.hub.testingBotKey | quote }}
        - name: TESTINGBOT_SECRET
          value: {{ .Values.hub.testingBotSecret | quote }}
        {{- end }}
        {{- if .Values.hub.lambdaTestEnabled }}
        - name: LT_ENABLED
          value: {{ .Values.hub.lambdaTestEnabled | quote }}
        - name: LT_USERNAME
          value: {{ .Values.hub.lambdaUsername | quote }}
        - name: LT_ACCESS_KEY
          value: {{ .Values.hub.lambdaAccessKey | quote }}
        {{- end }}
        {{- range $key, $val := .Values.hub.env }}
        - name: {{ $key | quote }}
          value: {{ $val | quote }}
        {{ end }}
      args:
        - start
      resources:
        {{- toYaml .Values.hub.resources | nindent 8 }}
      volumeMounts:
        - name: {{ template "zalenium.fullname" . }}-videos
          mountPath: /home/<USER>/videos
        - name: {{ template "zalenium.fullname" . }}-data
          mountPath: /tmp/mounted
  serviceAccountName: {{ template "zalenium.serviceAccountName" . }}
  {{- if .Values.nodeSelector.enabled }}
  nodeSelector:
    {{ .Values.nodeSelector.key }}: {{ .Values.nodeSelector.value | quote }}
  {{- end }}
  {{- if .Values.tolerations }}
  tolerations:
{{ toYaml .Values.tolerations | indent 4 }}
  {{- end }}
  {{- if .Values.affinity }}
  affinity:
{{ toYaml .Values.affinity | indent 4 }}
  {{- end -}}
{{- end }}
