{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  name: {{ template "zalenium.fullname" . }}
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
  annotations:
    {{- toYaml .Values.ingress.annotations | nindent 4 }}
spec:
  {{- if .Values.ingress.tls }}
  tls:
    - hosts:
      - {{ .Values.ingress.hostname | quote }}
      secretName: {{ .Values.ingress.secretName }}
  {{- end }}
  rules:
  - host: {{ .Values.ingress.hostname }}
    http:
      paths:
      - backend:
          serviceName: {{ template "zalenium.fullname" . }}
          servicePort: {{ .Values.hub.svcPort }}
        {{- if .Values.ingress.path }}
        path: {{ .Values.ingress.path }}
        {{- end }}
{{- end -}}