{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: {{ if .Values.rbac.clusterWideAccess }}"ClusterRoleBinding"{{ else }}"RoleBinding"{{ end }}
metadata:
  name: {{ template "zalenium.fullname" . }}
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: {{ if .Values.rbac.clusterWideAccess }}"ClusterRole"{{ else }}"Role"{{ end }}
  name: {{ template "zalenium.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "zalenium.serviceAccountName" . }}
  {{- if .Values.rbac.clusterWideAccess }}
  namespace: "{{ .Release.Namespace }}"
  {{- end }}
{{- end -}}
