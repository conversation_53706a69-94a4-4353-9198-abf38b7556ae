{{- if and (.Values.persistence.video.enabled) (not .Values.persistence.video.useExisting) }}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: "{{ template "zalenium.fullname" . }}-videos"
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.video.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.video.size | quote }}
  {{- if .Values.persistence.video.storageClass }}
  {{- if (eq "-" .Values.persistence.video.storageClass) }}
  storageClassName: ""
  {{- else }}
  storageClassName: "{{ .Values.persistence.video.storageClass }}"
  {{- end }}
  {{- end }}
{{- end }}