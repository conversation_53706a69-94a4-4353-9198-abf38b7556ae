apiVersion: v1
kind: Service
metadata:
  name: {{ template "zalenium.fullname" . }}
  labels:
    {{- include "zalenium.labels" . | nindent 4 }}
  {{- if .Values.hub.serviceAnnotations }}
  annotations:
    {{- toYaml .Values.hub.serviceAnnotations | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.hub.serviceType | quote }}
  {{- if eq .Values.hub.serviceType "LoadBalancer" }}
  loadBalancerSourceRanges: {{ .Values.hub.serviceSourceRanges }}
  {{- end }}
  sessionAffinity: {{ .Values.hub.serviceSessionAffinity | quote }}
  ports:
  - name: hub
    port: {{ .Values.hub.svcPort }}
    targetPort: {{ .Values.hub.port }}
  selector:
    app.kubernetes.io/name: {{ include "zalenium.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
