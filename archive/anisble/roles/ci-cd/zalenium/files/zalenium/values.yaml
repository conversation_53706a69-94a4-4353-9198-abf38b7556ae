hub:
  ## The repository and image
  ## ref: https://hub.docker.com/r/dosel/zalenium
  image: "dosel/zalenium"

  ## The tag for the image
  ## ref: https://hub.docker.com/r/dosel/zalenium/tags
  tag: "3"

  ## Specify a imagePullPolicy
  ## ref: http://kubernetes.io/docs/user-guide/images/#pre-pulling-images
  pullPolicy: "IfNotPresent"

  ## Specify secrets to pull images from private repositories
  ## ref: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod
  #imagePullSecret: "secret1"

  podAnnotations: {}

  ## The port which the hub listens on
  port: 4444

  ## Timeout for probe Hub liveness via HTTP request on Hub console
  livenessTimeout: 1

  ## Timeout for probe Hub readiness via HTTP request on Hub console
  readinessTimeout: 1

  ## Pod Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  ## in case it is running outside openshift, this should fix this https://github.com/zalando/zalenium/issues/631
  podSecurityContext: {}
    ## See all the options here https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.15/#podsecuritycontext-v1-core
    # fsGroup: 1000
    # runAsUser: 1000
    # runAsGroup: 1000

  ## Container Security Context
  containerSecurityContext: {}
    ## See all the options here https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.15/#securitycontext-v1-core
    # capabilities: []
    # privileged: false

  ## Configure resource requests and limits
  ## ref: http://kubernetes.io/docs/user-guide/compute-resources/
  resources:
    requests:
      cpu: "500m"
      memory: "500Mi"
  # For Java application, it's better to not currently put limit on CPU.
  #    limits:
  #      cpu: "1000m"
  #      memory: "2Gi"

  ## The type of service to create
  ##   Values: ClusterIP, NodePort, LoadBalancer, or ExternalName
  ## ref: https://kubernetes.io/docs/user-guide/services/
  serviceType: "NodePort"

  ## Port on which K8s service is listening on. Zalenium WebUI will be available there.
  svcPort: 80

  ## If serviceType is LoadBalancer:
  ##   Add list of IPs allowed to connect to the service
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
  serviceSourceRanges: ["0.0.0.0/0"]

  ## Control where client requests go, to the same pod or round-robin
  ##   Values: ClientIP or None
  ## ref: https://kubernetes.io/docs/user-guide/services/
  serviceSessionAffinity: "None"

  # Annotations to be added to the service.
  ##
  serviceAnnotations: {}
  # Below we expose metrics to Prometheus.
  #   prometheus.io/path: /metrics
  #   prometheus.io/scrape: "true"

  ## Environment variables passed to Zalenium hub.
  ## https://github.com/zalando/zalenium/blob/master/docs/usage_examples.md
  desiredContainers: 2
  maxDockerSeleniumContainers: 10
  videoRecordingEnabled: true
  # the limits/requests below are for Selenium pods (containers)
  cpuRequest: 250m
  cpuLimit: 1000m
  memRequest: 500Mi
  memLimit: 2Gi
  screenWidth: 1440
  screenHeight: 900
  timeZone: "UTC"
  seleniumImageName: "elgalu/selenium"
  maxTestSessions: 1
  newSessionsWaitTimeout: 600000
  debugEnabled: false
  keepOnlyFailedTests: false
  retentionPeriod: 3
  sendAnonymousUsageInfo: true
  basicAuth:
    enabled: false
    username: "zalenium"
    password: "password"
  sauceLabsEnabled: false
  sauceUserName: blank
  sauceAccessKey: blank
  browserStackEnabled: false
  browserStackUser: blank
  browserStackKey: blank
  testingBotEnabled: false
  testingBotKey: blank
  testingBotSecret: blank
  lambdaTestEnabled: false
  lambdaUsername: blank
  lambdaAccessKey: blank

  ## Arbitrary environment variables
  env:
    # FOO: BAR

  ## Use Openshift DeploymentConfig instead of Kubernetes Deployment
  ## https://docs.okd.io/latest/architecture/core_concepts/deployments.html#deployments-and-deployment-configurations
  openshift:
    deploymentConfig:
      enabled: false
      triggers:
        - type: ConfigChange
    route:
      enabled: false
      hostname:
      tls:
      #  termination: edge
      #  insecureEdgeTerminationPolicy: Redirect

## Enable persistence using Persistent Volume Claims
## ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
##
persistence:
  data:
    enabled: false
    ## If true will use existing PVC instead of creating one
    useExisting: false
    ## Name of existing PVC to be used in the zalenium deployment
    name:
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    # storageClass: "-"
    accessMode: ReadWriteOnce
    size: 2Gi
  video:
    enabled: false
    ## If true will use existing PVC instead of creating one
    useExisting: false
    ## Name of existing PVC to be used in the zalenium deployment
    name:
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    # storageClass: "-"
    accessMode: ReadWriteOnce
    size: 10Gi

ingress:
  enabled: false
  tls: false
  # secretName: my-tls-cert # only needed if tls above is true
  hostname: zalenium.foobar.com
  # If your ingress host name is shared with multiple
  # applications, Enter a path starting with / (eg. /zalenium)
  # If your ingress host name is only for Zalenium,
  # no need to set path
  # path: /
  # maxBodySize: 300M
  annotations:
  # kubernetes.io/ingress.class: "nginx"
  # kubernetes.io/tls-acme: "true"
  ## For RBAC support:

rbac:
  create: true
  ## Run the zalenium hub container with the ability to deploy/manage containers of jobs
  ## cluster-wide or only within namespace
  clusterWideAccess: false

serviceAccount:
  create: true
  ## Use the following Kubernetes Service Account name if not created
  name:

nodeSelector:
  enabled: false
  ## Run zalenium hub on specified nodes
  ## key: test
  ## value: test

# Allow pods to run on nodes with taints
# https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
#
# tolerations:
# - key: "<key-name>"
#   operator: "Equal"
#   value: "<key-value>"
#   effect: "NoSchedule"

# More flexible way to manage pods' allocation
# https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
#
# affinity:
#   # soft preference to go to dedicated nodes
#   nodeAffinity:
#     preferredDuringSchedulingIgnoredDuringExecution:
#     - weight: 100
#       preference:
#         matchExpressions:
#         - key: <key-name>
#           operator: In
#           values:
#           - <key-value>
#   # prefer not to start the pod on a node that's having another pod with specific labels
#   podAntiAffinity:
#     preferredDuringSchedulingIgnoredDuringExecution:
#     - weight: 100
#       podAffinityTerm:
#         labelSelector:
#           matchExpressions:
#           - key: <key-name>
#             operator: In
#             values:
#             - <key-value>
#         topologyKey: "kubernetes.io/hostname"
