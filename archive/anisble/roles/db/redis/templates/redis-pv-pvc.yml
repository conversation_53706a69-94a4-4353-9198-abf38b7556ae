apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv
  labels:
    app: redis
spec:
  capacity:
    storage: {{volume_xs}}
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: {{cluster_data_dir}}/redis-pv
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists

---

kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: redis-pvc
  namespace: redis
spec:
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{volume_xs}}
  storageClassName: local-storage
  selector:
    matchLabels:
      app: redis
