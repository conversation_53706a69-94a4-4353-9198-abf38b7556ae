---
- name: Create redis pv directory
  file:
    path: "{{cluster_data_dir}}/redis-pv"
    state: directory

- name: Create redis local persistent volume
  shell: kubectl apply --namespace redis -f -
  args:
    stdin: "{{ lookup('template', '{{db_dir}}/redis/templates/redis-pv-pvc.yml')}}"

- name: Deploy redis
  shell: helm install redis --namespace redis -f - bitnami/redis --version 10.5.13
  args:
    stdin: "{{ lookup('file', '{{db_dir}}/redis/templates/redis-overrides.yml')}}"
