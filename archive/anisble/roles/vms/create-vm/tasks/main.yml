- name: Remove previous {{vm_img}} from {{vms_dir}}
  file:
    path: '{{vms_dir}}/{{vm_img}}'
    state: absent
  ignore_errors: true

- name: Download {{vm_img}} for creation {{vm_vdi}}
  shell: virt-builder {{vm_version}} --format raw
  args:
    chdir: "{{vms_dir}}"

- name: Poweroff {{node_name}} on host
  shell: VBoxManage controlvm {{node_name}} poweroff
  ignore_errors: true

- name: Unregister {{node_name}} from host
  shell: VBoxManage unregistervm {{node_name}}
  ignore_errors: true

- name: Delete {{virtualbox_vms_dir}}/{{node_name}} from host
  file:
    path: "{{virtualbox_vms_dir}}/{{node_name}}"
    state: absent

- name: Remove previous {{vm_vdi}} from {{vms_dir}}
  file:
    path: '{{vms_dir}}/{{vm_vdi}}'
    state: absent
  ignore_errors: true

# # use only if cluster-data(-staging).vdi exists and should be deleted from {{hdd_dir}} on host
# - name: Unregister {{cluster_data_vdi}} from virtual box
#   shell: VBoxManage closemedium disk {{cluster_data_vdi}} --delete
#   ignore_errors: true

# - name: Remove previous {{cluster_data_vdi}} from {{hdd_dir}}
#   file:
#     path: "{{cluster_data_vdi}}"
#     state: absent
#   ignore_errors: true

# # use only if fast-cluster-data(-staging).vdi exists and should be deleted from {{ssd2_dir}} on host
# - name: Unregister {{fast_cluster_data_vdi}} from virtual box
#   shell: VBoxManage closemedium disk {{fast_cluster_data_vdi}} --delete
#   ignore_errors: true

# - name: Remove previous {{fast_cluster_data_vdi}} from {{ssd2_dir}}
#   file:
#     path: "{{fast_cluster_data_vdi}}"
#     state: absent
#   ignore_errors: true

# LIBGUESTFS_BACKEND_SETTINGS used to avoid https://bugzilla.redhat.com/show_bug.cgi?id=1277744
- name: Setup root node_password in {{vm_img}}
  shell: LIBGUESTFS_BACKEND_SETTINGS=force_tcg virt-customize --format raw -a {{vms_dir}}/{{vm_img}} --root-password password:{{node_password}} --hostname {{node_hostname}}

- name: Inject openssh-server into {{vm_img}}
  shell: LIBGUESTFS_BACKEND_SETTINGS=force_tcg virt-customize --format raw -a {{vms_dir}}/{{vm_img}} --install openssh-server

- name: Copy 01-netcfg.yaml and sshd_config to host
  template:
    src: "{{item}}"
    dest: "{{vms_dir}}"
  with_fileglob:
    - ../templates/*

- name: Add 0640 attribute to {{vms_dir}}/01-netcfg.yaml
  become: yes
  file:
    path: "{{vms_dir}}/01-netcfg.yaml"
    owner: root
    group: root
    mode: '0640'

- name: Add 0644 attribute to {{vms_dir}}/sshd_config
  become: yes
  file:
    path: "{{vms_dir}}/sshd_config"
    owner: root
    group: root
    mode: '0640'

- name: Copy-in 01-netcfg.yaml, sshd_config to {{vm_img}}
  become: yes
  shell: LIBGUESTFS_BACKEND_SETTINGS=force_tcg virt-customize --format raw -a {{vms_dir}}/{{vm_img}} --copy-in {{vms_dir}}/01-netcfg.yaml:/etc/netplan/ --copy-in {{vms_dir}}/sshd_config:/etc/ssh/

- name: Convert {{vm_img}} to {{vm_vdi}}
  shell: VBoxManage convertdd {{vms_dir}}/{{vm_img}} {{vms_dir}}/{{vm_vdi}}

- name: Resize {{vms_dir}}/{{vm_vdi}}
  shell: VBoxManage modifymedium disk {{vms_dir}}/{{vm_vdi}} --resize {{vm_ssd_space | default(163840)}}

- name: Create vm {{node_name}}
  shell: VBoxManage createvm --name {{node_name}} --ostype Ubuntu_64 --register

- name: Add SATA storage controller to vm {{node_name}}
  shell: VBoxManage storagectl {{node_name}} --name SATA --add sata --controller LSILogicSAS --bootable on --portcount 3

- name: Attach {{vm_vdi}} to vm {{node_name}}
  shell: VBoxManage storageattach {{node_name}} --storagectl SATA --port 0 --device 0 --type hdd --medium {{vms_dir}}/{{vm_vdi}} --nonrotational on

# # use only if cluster-data(-staging).vdi does not exist in {{hdd_dir}} on host
# - name: Create {{cluster_data_vdi}}
#   shell: VBoxManage createmedium disk --filename {{cluster_data_vdi}} --size {{vm_hdd_space | default(245760)}} --format VDI

# # use only if fast-cluster-data(-staging).vdi does not exist in {{ssd2_dir}} on host
# - name: Create {{fast_cluster_data_vdi}}
#   shell: VBoxManage createmedium disk --filename {{fast_cluster_data_vdi}} --size {{vm_ssd2_space | default(163840)}} --format VDI

- name: Attach {{cluster_data_vdi}} to vm {{node_name}}
  shell: VBoxManage storageattach {{node_name}} --storagectl SATA --port 1 --device 0 --type hdd --medium {{cluster_data_vdi}}

- name: Attach {{fast_cluster_data_vdi}} to vm {{node_name}}
  shell: VBoxManage storageattach {{node_name}} --storagectl SATA --port 2 --device 0 --type hdd --medium {{fast_cluster_data_vdi}} --nonrotational on

- name: Configure vm {{node_name}}
  shell: VBoxManage modifyvm {{node_name}} --memory {{vm_ram_amount | default(16384)}} --cpus {{vm_cpu_amount | default(3)}}

- name: Networking1 on vm {{node_name}}
  shell: VBoxManage modifyvm {{node_name}} --nic1 nat --nictype1 82540EM --cableconnected1 on

# not needed anymore
# - name: Forward openvpn port on vm {{node_name}}
#   shell: VBoxManage modifyvm {{node_name}} --natpf1 "openvpn-tcp,tcp,,30502,,30502" --natpf1 "openvpn-udp,udp,,30502,,30502" --natpf1 "ssh,tcp,,2222,,22"


# for wifi adapter in vbox change adapter2 bridge to wifi adapter on ui
- name: Networking2 on vm {{node_name}}
  shell: VBoxManage modifyvm {{node_name}} --nic2 bridged --nictype2 82543GC --bridgeadapter2 eno1 --cableconnected2 on

- name: Create file /etc/default/virtualbox
  become: yes
  file:
    path: "/etc/default/virtualbox"
    state: touch

- name: Insert block in file /etc/default/virtualbox
  become: yes
  blockinfile:
    dest: "/etc/default/virtualbox"
    block: |
      VBOXAUTOSTART_DB=/etc/vbox
      VBOXAUTOSTART_CONFIG=/etc/vbox/autostart.cfg
    marker: ""

- name: Create file /etc/vbox/autostart.cfg
  become: yes
  file:
    path: "/etc/vbox/autostart.cfg"
    state: touch

- name: Insert block in file /etc/vbox/autostart.cfg
  become: yes
  blockinfile:
    dest: "/etc/vbox/autostart.cfg"
    block: |
      default_policy = deny
      {{ansible_user}} = {
      allow = true
      }
    marker: ""

- name: Give permissions to vboxusers group to access /etc/vbox
  become: yes
  file:
    path: /etc/vbox
    group: vboxusers
    mode: '0775'

- name: Add existing user {{ansible_user}} to group vboxusers
  become: yes
  shell: usermod -aG vboxusers {{ansible_user}}

- name: Set property autostartdbpath
  shell: VBoxManage setproperty autostartdbpath /etc/vbox

- name: Set {{node_name}} --autostart-enabled on
  shell: VBoxManage modifyvm {{node_name}} --autostart-enabled on
  when: node_name != "km-staging"

- name: Restart service vboxautostart-service
  become: yes
  service:
    name: vboxautostart-service
    state: restarted

- name: Start {{node_name}} vm
  shell: vboxmanage startvm {{node_name}} --type=headless
  ignore_errors: true

- pause:
    seconds: 30

- name: Delete /root/.ssh/known_hosts
  become: yes
  file:
    path: "/root/.ssh/known_hosts"
    state: absent
  ignore_errors: true

- name: Delete {{home_dir}}/.ssh/known_hosts
  file:
    path: "{{home_dir}}/.ssh/known_hosts"
    state: absent
  ignore_errors: true

- name: Install sshpass
  become: yes
  apt:
    name: sshpass
    state: latest

- name: set queue_depth value in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'echo 1 > /sys/block/sda/device/queue_depth'

- name: Install cloud-guest-utils into vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'apt-get install -y cloud-guest-utils'

- name: Grow partition /dev/sda1 in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'growpart /dev/sda 2'

- name: Grow partition /dev/sda1 in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'growpart /dev/sda 5'

- name: Resize2fs /dev/sda1 in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'resize2fs /dev/sda5'

- name: Create user in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'adduser --disabled-password --gecos "" {{node_user}} ; echo -e "{{node_password}}\n{{node_password}}" | passwd {{node_user}}'

- name: Add node_user to sudo group in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no root@{{node_ip}} 'usermod -aG sudo {{node_user}}'

- name: Make dir .ssh in vm
  shell: sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no {{node_user}}@{{node_ip}} 'mkdir -p /home/<USER>/.ssh'

- name: Get {{sources_dir}} id_rsa.pub
  become: yes
  command: cat {{sources_dir}}/id_rsa.pub
  register: sources_dir_pub_key
  delegate_to: localhost

- name: Get id_rsa.pub
  become: yes
  command: cat {{home_dir}}/.ssh/id_rsa.pub
  register: ubuntu_pub_key

- name: Create temp file
  become: yes
  file:
    path: "/var/tmp/keys"
    state: touch

- name: Insert {{sources_dir}} id_rsa.pub into temp file
  become: yes
  lineinfile:
    dest: "/var/tmp/keys"
    line: "{{sources_dir_pub_key.stdout}}"
    create: yes

- name: Insert id_rsa.pub into temp file
  become: yes
  lineinfile:
    dest: "/var/tmp/keys"
    line: "{{ubuntu_pub_key.stdout}}"
    create: yes

- name: Copy temp file content to authorized_keys in vm
  become: yes
  shell: cat /var/tmp/keys | sshpass -p {{node_password}} ssh -o StrictHostKeyChecking=no {{node_user}}@{{node_ip}} "cat >> /home/<USER>/.ssh/authorized_keys"

- name: Delete temp file
  become: yes
  file:
    path: "/var/tmp/keys"
    state: absent
