# # Security risk do not enable till Tweak SSD

# - name: Install ubuntu-desktop on ubuntu
#   become: yes
#   apt:
#     update_cache: yes
#     name: ubuntu-desktop
#     state: latest

# - name: Install xrdp on ubuntu
#   become: yes
#   apt:
#     update_cache: yes
#     name: xrdp
#     state: latest

# - name: Enable xrdp
#   become: yes
#   systemd:
#     name: xrdp
#     enabled: yes

# - name: Install xfce4 on ubuntu
#   become: yes
#   apt:
#     name: xfce4
#     state: latest

# - name: Install xfce4-terminal on ubuntu
#   become: yes
#   apt:
#     name: xfce4-terminal
#     state: latest

# - name: Add startxfce4 to startwm.sh
#   become: yes
#   lineinfile:
#     path: /etc/xrdp/startwm.sh
#     line: startxfce4
#     create: yes

# - name: Restart service xrdp
#   become: yes
#   service:
#     name: xrdp
#     state: restarted

- name: Set timezone to UTC
  become: yes
  shell: timedatectl set-timezone UTC

- name: Tweak ssd on ubuntu
  become: yes
  shell: cp /etc/fstab /etc/fstab.bak && sed -i -e 's/errors=remount-ro/noatime,errors=remount-ro/g' /etc/fstab

- name: Disable NCQ on sda
  become: yes
  shell: echo 1 > /sys/block/sda/device/queue_depth

- name: Insert poweroff km-staging cron job to ubuntu crontab
  become: yes
  lineinfile:
    create: yes
    path: /etc/crontab
    line: "{{item}}"
  with_items:
    - '0 0 * * * vboxmanage list runningvms | grep km-staging && vboxmanage controlvm km-staging acpipowerbutton'

- name: Insert Docker prune job to ubuntu crontab
  become: yes
  lineinfile:
    create: yes
    path: /etc/crontab
    line: "{{item}}"
  with_items:
    - '0 3 * * * root docker-machine ssh "docker system prune -a -f"'

- name: Add vbox key1
  become: yes
  apt_key:
    url: https://www.virtualbox.org/download/oracle_vbox_2016.asc
    state: present

- name: Add vbox key2
  become: yes
  apt_key:
    url: https://www.virtualbox.org/download/oracle_vbox.asc
    state: present

- name: Add vbox apt repository
  become: yes
  apt_repository:
    repo: 'deb http://download.virtualbox.org/virtualbox/debian {{ansible_distribution_release}} contrib'
    state: present
    filename: virtualbox.list
    update_cache: yes

- name: Get kernel version
  become: yes
  shell: uname -r
  register: kernel_version

- name: Install linux-headers-{{kernel_version.stdout}}
  become: yes
  apt:
    update_cache: yes
    name: linux-headers-{{kernel_version.stdout}}
    state: latest

- name: Install dkms package
  become: yes
  apt:
    name: dkms
    state: latest

- name: Install virtualbox-6.1
  become: yes
  apt:
    update_cache: yes
    name: virtualbox-6.1
    state: latest
  ignore_errors: true

- name: Install libguestfs-tools
  become: yes
  apt:
    name: libguestfs-tools
    state: latest

- name: Create {{vms_dir}} directory
  file:
    path: "{{vms_dir}}"
    state: directory

- name: Download virtualbox-ext-pack
  become: yes
  get_url:
    url: https://download.virtualbox.org/virtualbox/6.1.0/Oracle_VM_VirtualBox_Extension_Pack-6.1.0.vbox-extpack
    dest: "{{vms_dir}}"

- name: Install virtualbox-ext-pack
  become: yes
  shell: echo "y" | VBoxManage extpack install --replace {{vms_dir}}/Oracle_VM_VirtualBox_Extension_Pack-6.1.0.vbox-extpack

# todo fd sec 1 is last 4 required?
- name: Add 0644 attribute to /boot/vmlinuz-{{kernel_version.stdout}}
  become: yes
  file:
    path: /boot/vmlinuz-{{kernel_version.stdout}}
    mode: '0644'
