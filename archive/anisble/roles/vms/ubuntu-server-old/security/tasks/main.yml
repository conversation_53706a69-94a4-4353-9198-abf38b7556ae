- name: disable ssh root login
  replace:
    path: /etc/ssh/sshd_config
    regexp: 'PermitRootLogin prohibit-password'
    replace: 'PermitRootLogin no'

- name: allow ssh port
  become: yes
  shell: ufw allow 22/tcp

- name: disable traffic from 192.168.1.50 (km)
  become: yes
  lineinfile:
    path: /etc/ufw/before.rules
    line: '-A ufw-before-input -s 192.168.1.50 -j DROP'
    insertafter: '# End required lines'

- name: Enable firewall
  become: yes
  shell: yes | sudo ufw enable
