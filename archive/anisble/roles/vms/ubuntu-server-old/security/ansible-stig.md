### Priority 1
* Password master switch (V-71903) - security_pwquality_apply_rules: yes
* The operating system must enable an application firewall, if available. (V-72273)
* Systems with a Basic Input/Output System (BIOS) must require authentication upon booting into single-user and maintenance modes. (V-71961)
* Systems with a Basic Input/Output System (BIOS) must require authentication upon booting into single-user and maintenance modes. (V-71961)

### Think about

* Users must provide a password for privilege escalation. (V-71947)
* Users must re-authenticate for privilege escalation. (V-71949)

* The host must be configured to prohibit or restrict the use of functions, ports, protocols, and/or services, as defined in the Ports, Protocols, and Services Management Component Local Service Assessment (PPSM CLSA) and vulnerability assessments. (V-72219)
* The operating system must prevent non-privileged users from executing privileged functions to include disabling, circumventing, or altering implemented security safeguards/countermeasures. (V-71971)
* Wireless network adapters must be disabled. (V-73177)
* File systems that contain user home directories must be mounted to prevent files with the setuid and setgid bit set from being executed. (V-72041)
* File systems that are used with removable media must be mounted to prevent files with the setuid and setgid bit set from being executed. (V-72043)
* The operating system must require authentication upon booting into single-user and maintenance modes. (V-77823)
* A File Transfer Protocol (FTP) server package must not be installed unless needed. (V-72299)

## Opt In
* Passwords for new users must be restricted to a 24 hours/1 day minimum lifetime. (V-71925)
* Passwords must be restricted to a 24 hours/1 day minimum lifetime. (V-71927)
* Passwords for new users must be restricted to a 60-day maximum lifetime. (V-71929)
* Existing passwords must be restricted to a 60-day maximum lifetime. (V-71931)
* Passwords must be prohibited from reuse for a minimum of five generations. (V-71933)
* Passwords must be a minimum of 15 characters in length. (V-71935) - 12 chars
* When passwords are changed or new passwords are established, pwquality must be used. (V-73159)
* There must be no .shosts files on the system. (V-72277)
* The operating system must define default permissions for all authenticated users in such a way that the user can only read and modify their own files. (V-71995)
* Vendor packaged system security patches and updates must be installed and up to date. (V-71999)
* The operating system must disable account identifiers (individuals, groups, roles, and devices) if the password expires. (V-71941)
* The cryptographic hash of system files and commands must match vendor values. (V-71855)
* USB mass storage must be disabled. (V-71983)
* The operating system must prevent the installation of software, patches, service packs, device drivers, or operating system components of packages without verification of the repository metadata. (V-71981)
* A file integrity tool must verify the baseline operating system configuration at least weekly. (V-71973)
* All uses of the chown command must be audited. (V-72097) - and others
* The system must use a virus scan program. (V-72213)
* The operating system must limit the number of concurrent sessions to 10 for all accounts and/or account types. (V-72217)
* All local interactive user home directories must have mode 0750 or less permissive. (V-72017)
* All local interactive user home directories must be owned by their respective users. (V-72019)
* All local interactive user home directories must be group-owned by the home directory owners primary group. (V-72021)
* All world-writable directories must be group-owned by root, sys, bin, or an application group. (V-72047)
* All files and directories must have a valid owner. (V-72007)
* All files and directories must have a valid group owner. (V-72009)
* The operating system must remove all software components after updated versions have been installed. (V-71987)
* The operating system must off-load audit records onto a different system or media from the system being audited. (V-72083)
* The operating system must encrypt the transfer of audit records off-loaded onto a different system or media from the system being audited. (V-72085)
* The file permissions, ownership, and group membership of system files and commands must match the vendor values. (V-71849) - not used on Ubuntu
* The operating system must protect against or limit the effects of Denial of Service (DoS) attacks by validating the operating system is implementing rate-limiting measures on impacted network interfaces. (V-72271) - do not use
* The system must not be performing packet forwarding unless the system is a router. (V-72309) - probably ok

# Not used

## Exception - Initial Provisioning

### Low prio

* A separate file system must be used for user home directories (such as /home or an equivalent). (V-72059)
* The system must use a separate file system for /var. (V-72061)
* The system must use a separate file system for the system audit data path. (V-72063)
* The system must use a separate file system for /tmp (or equivalent). (V-72065)
* The system must not allow removable media to be used as the boot loader unless approved. (V-72075)

## Exception - Manual Intervention

### Low prio

* The operating system must uniquely identify and must authenticate organizational users (or processes acting on behalf of organizational users) using multifactor authentication. (V-71965)
* The system must not have unnecessary accounts. (V-72001)
* All files and directories contained in local interactive user home directories must be owned by the owner of the home directory. (V-72023)
* All files and directories contained in local interactive user home directories must be group-owned by a group of which the home directory owner is a member. (V-72025)
* All files and directories contained in local interactive user home directories must have mode 0750 or less permissive. (V-72027)
* All local initialization files for interactive users must be owned by the home directory user or root. (V-72029)
* Local initialization files for local interactive users must be group-owned by the users primary group or root. (V-72031)
* All local initialization files must have mode 0740 or less permissive. (V-72033)
* All local interactive user initialization files executable search paths must contain only paths that resolve to the users home directory. (V-72035)
* Local initialization files must not execute world-writable programs. (V-72037)
* The umask must be set to 077 for all local interactive user accounts. (V-72049)
* Cron logging must be implemented. (V-72051)
* All privileged function executions must be audited. (V-72095)
* The rsyslog daemon must not accept log messages from other servers unless the server is being used for log aggregation. (V-72211)
* The system access control program must be configured to grant or deny system access to specific hosts and services. (V-72315)
* The system must not have unauthorized IP tunnels configured. (V-72317)
* The operating system must implement multifactor authentication for access to privileged accounts via pluggable authentication modules (PAM). (V-72427)
* The operating system must implement certificate status checking for PKI authentication. (V-72433)
* The operating system must implement smart card logons for multifactor authentication for access to privileged accounts. (V-72435)
* The operating system must uniquely identify and must authenticate users using multifactor authentication via a graphical user logon. (V-77819)


### Not using subsystem
* The operating system must implement cryptography to protect the integrity of Lightweight Directory Access Protocol (LDAP) authentication communications. (V-72227)
* The operating system must implement cryptography to protect the integrity of Lightweight Directory Access Protocol (LDAP) communications. (V-72229)
* The operating system must implement cryptography to protect the integrity of Lightweight Directory Access Protocol (LDAP) communications. (V-72231)
* The Network File System (NFS) must be configured to use RPCSEC_GSS. (V-72311)
* File systems that are being imported via Network File System (NFS) must be mounted to prevent files with the setuid and setgid bit set from being executed. (V-72045)
* File systems that are being imported via Network File System (NFS) must be mounted to prevent binary files from being executed. (V-73161)

## Verification Only

* The system must send rsyslog output to a log aggregation server. (V-72209)
* The system must display the date and time of the last successful account logon upon logon. (V-72275)
* Network interfaces must not be in promiscuous mode. (V-72295)

