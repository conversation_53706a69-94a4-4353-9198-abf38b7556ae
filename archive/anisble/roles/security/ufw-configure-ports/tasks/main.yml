- name: allow port 2222 - ssh
  ufw: rule=allow port=2222 proto=tcp

- name: allow port 22 ssh
  ufw: rule=allow port=22 proto=tcp

- name: allow port 8001 - kube web
  ufw: rule=allow port=8001 proto=tcp

- name: allow port 443 https
  ufw: rule=allow port=443 proto=tcp

- name: allow 80 http
  ufw: rule=allow port=80 proto=tcp

- name: allow 30500 registry
  ufw: rule=allow port=30500 proto=tcp

- name: allow 30501 registry
  ufw: rule=allow port=30501 proto=tcp

- name: allow 30502 registry
  ufw: rule=allow port=30502 proto=tcp

- name: allow 30503 openvpn2
  ufw: rule=allow port=30503 proto=tcp

- name: allow 30504 ingress http
  ufw: rule=allow port=30504 proto=tcp

- name: allow 30900 port
  ufw: rule=allow port=30900 proto=tcp

- name: allow 30902 grafana
  ufw: rule=allow port=30902 proto=tcp

- name: allow 30903 alertmanager
  ufw: rule=allow port=30903 proto=tcp

- name: allow 30904 port
  ufw: rule=allow port=30904 proto=tcp

- name: allow 30905 elasticsearch
  ufw: rule=allow port=30905 proto=tcp

- name: allow 31000 ks-web
  ufw: rule=allow port=31000 proto=tcp

- name: allow 31001 zalenium
  ufw: rule=allow port=31001 proto=tcp

- name: allow 31002 ks-ui-tests
  ufw: rule=allow port=31002 proto=tcp

- name: allow 31003 postgres
  ufw: rule=allow port=31003 proto=tcp

- name: allow 31004 ks-static-file-server
  ufw: rule=allow port=31004 proto=tcp

- name: allow 31005 redash
  ufw: rule=allow port=31005 proto=tcp

- name: allow 31006 redis
  ufw: rule=allow port=31006 proto=tcp

- name: allow 31007 ks-web-prod
  ufw: rule=allow port=31007 proto=tcp

- name: allow 31008 ks-web-staging
  ufw: rule=allow port=31008 proto=tcp

- name: allow 31009 sonarqube
  ufw: rule=allow port=31009 proto=tcp

- name: allow 31010 zimbra-web
  ufw: rule=allow port=31010 proto=tcp

- name: Disable default in
  ufw: direction=incoming policy=deny

- name: enable ufw services
  ufw: state=enabled
