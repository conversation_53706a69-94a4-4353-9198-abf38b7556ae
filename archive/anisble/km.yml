- hosts: km
  gather_facts: yes
  roles:
    # - commons/ssh-keys
    # - vms/configure-vm
    # - commons/dotfiles
    # - commons/packages
    # - linux/connect-hdd
    # - linux/connect-ssd2
    # - kube/docker
    # - kube/install
    # - kube/init-master
    # - kube/cni
    # - kube/config-master
    # - kube/registry
    # - create-secrets-prod
    # - kube/nginx-ingress
    # - db/postgres
    # - ops/logging
    # - ops/monitoring
    # - ops/pluto
    # - apps/sonarqube
    # - ci-cd/zalenium
    # - ci-cd/jenkins


# ./hack/copy-vagrant-key-km.sh p
# ./hack/post-init-km.sh
# vagrant ssh -c "(cd /usr/src/cluster/ansible && sudo ansible-playbook km.yml)"
# vagrant ssh -c "sudo rm /root/.ssh/known_hosts"
# rm /c/Users/<USER>/.ssh/known_hosts
