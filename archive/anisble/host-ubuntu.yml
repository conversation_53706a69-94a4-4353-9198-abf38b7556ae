- hosts: ubuntu
  gather_facts: yes
  roles:
    # - host/host-config
    # - linux/connect-hdd
    # - linux/connect-ssd2
    # todo fd sec is it secure?
    # - commons/ssh-keys
    # - commons/packages
    # - commons/dotfiles
    # - { role: vms/create-vm, vars: [node_ip: ************] }
    # - { role: vms/create-vm, vars: [node_ip: ************, node_name: "km-staging", node_hostname: "km-staging", vms_dir: "{{ssd2_dir}}/vms", vm_vdi: "{{vm_version}}-staging.vdi", cluster_data_vdi: "{{hdd_dir}}/cluster-data-staging.vdi", fast_cluster_data_vdi: "{{ssd2_dir}}/fast-cluster-data-staging.vdi", vm_ssd_space: 50720, vm_hdd_space: 60960, vm_ssd2_space: 40720, vm_ram_amount: 12288, vm_cpu_amount: 2] }
    # - host/security
    # - ops/backup

# vagrant ssh -c "(cd /usr/src/cluster/ansible && sudo ansible-playbook ubuntu.yml)"
# ssh ubuntu@************ "rm /home/<USER>/.ssh/known_hosts"



## todo fd sec protect ubuntu from ipspoofing from km
