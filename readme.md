# Setup

Go to `docs/windows.md` and do sections `Packages for local windows development` and
`WSL initial setup`

Go to `roles/kube/talos/talos.md` and do `Local talos install` section

# Setup (old, not used anymore)

For setup use section [**Start work with cluster**](docs/tutorials/local-kubernetes.md)

# Clusters

## km-vps

*************

ssh km@*************

kubectl config use-context kubernetes-admin@kubernetes

## km-staging

*************

ssh km@*************

kubectl config use-context kubernetes-admin@kubernetes-staging

## km-blue-staging

*************

kubectl config use-context admin@talos-vbox-cluster

## km-blue

**************

kubectl config use-context admin@km-blue

## Ingress static ip

************
